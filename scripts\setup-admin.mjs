import { initializeApp } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';
import { getFunctions, httpsCallable } from 'firebase/functions';

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyBOJGGJGJGJGJGJGJGJGJGJGJGJGJGJGJG",
  authDomain: "skinglow1000.firebaseapp.com",
  projectId: "skinglow1000",
  storageBucket: "skinglow1000.firebasestorage.app",
  messagingSenderId: "502278853344",
  appId: "1:502278853344:web:abc123def456ghi789"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const functions = getFunctions(app);

async function setupAdmin() {
  try {
    console.log('Setting up admin privileges...');
    
    // You need to be logged in first
    console.log('Please make sure you are logged in to your app first.');
    console.log('Then run the initializeAdminCallable function from the browser console.');
    
    const initializeAdmin = httpsCallable(functions, 'initializeAdminCallable');
    const result = await initializeAdmin();
    
    console.log('✅ Admin setup successful:', result.data);
  } catch (error) {
    console.error('❌ Admin setup failed:', error);
  }
}

setupAdmin();
