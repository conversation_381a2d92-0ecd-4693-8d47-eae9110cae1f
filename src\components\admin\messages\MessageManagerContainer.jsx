import React, { useState, useEffect, useRef } from "react";
import styled, { createGlobalStyle } from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaBars,
  FaSearch,
  FaTimes,
  FaSortAmountDown,
  FaSortAmountUp,
  FaCalendarAlt,
  FaCheckSquare, // Add this for selection mode
  FaRegSquare, // Add this for non-selection mode
} from "react-icons/fa";
import { colors, spacing, breakpoints } from "../../../styles";
import DeleteConfirmationModal from "../../ui/DeleteConfirmationModal";
import { BatchActions } from "./MessageActions";
import MessageFilters from "./MessageFilters";
import MessageExport from "./MessageExport";
import MessageList from "./MessageList";
import {
  fetchMessagesFromDB,
  toggleReadStatus,
  toggleStarStatus,
  toggleArchiveStatus,
  deleteMessage,
  getFilteredMessages,
  batchToggleReadStatus,
  batchToggleStarStatus,
  batchToggleArchiveStatus,
  batchDeleteMessages,
} from "./MessageUtils";
import KeyboardShortcutsHelp from "./KeyboardShortcutsHelp";

const Container = styled.div`
  padding: ${spacing.lg};
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.lightest};
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  @media (max-width: ${breakpoints.md}) {
    padding: ${spacing.md};
  }
`;

const SectionTitle = styled.h2`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin-bottom: ${spacing.md};
  font-weight: 600;
`;

const MessageLayout = styled.div`
  display: grid;
  grid-template-columns: ${(props) =>
    props.sidebarOpen ? "250px 1fr" : "60px 1fr"};
  gap: ${spacing.md};
  transition: grid-template-columns 0.3s ease;

  @media (max-width: ${breakpoints.md}) {
    display: ${(props) => (props.mobileDropdownOpen ? "grid" : "block")};
    grid-template-columns: ${(props) =>
      props.sidebarOpen ? "250px 1fr" : "60px 1fr"};
  }
`;

const MobileSidebarToggle = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${spacing.sm};
  background: ${(props) =>
    props.darkMode ? colors.primary.dark : colors.primary.light};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.primary.dark};
  border: none;
  border-radius: 4px;
  cursor: pointer;

  @media (max-width: ${breakpoints.md}) {
    display: flex;
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 18px;
`;

const FilterControls = styled.div`
  display: flex;
  gap: ${spacing.md};
  margin-bottom: ${spacing.md};
  align-items: center;
  flex-wrap: wrap;

  @media (max-width: ${breakpoints.md}) {
    gap: ${spacing.sm};
  }
`;

const SearchContainer = styled.div`
  position: relative;
  flex: 1;
  max-width: 400px;
  margin-right: ${spacing.md};
`;

const DateFilterWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
`;

const FilterSelect = styled.select`
  padding: ${spacing.sm};
  border-radius: 4px;
  border: 1px solid
    ${(props) =>
      props.darkMode ? colors.neutral.medium : colors.neutral.light};
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  appearance: none;
  padding-right: 30px;
  background-image: ${(props) =>
    props.darkMode
      ? `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23ffffff' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E")`
      : `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23000000' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E")`};
  background-repeat: no-repeat;
  background-position: right 10px center;

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }

  option {
    background: ${(props) =>
      props.darkMode ? colors.neutral.darker : colors.neutral.white};
    color: ${(props) =>
      props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${spacing.sm};
  top: 58%;
  transform: translateY(-50%);
  color: ${(props) =>
    props.darkMode ? colors.primary.darker : colors.neutral.main};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${spacing.sm} ${spacing.sm} ${spacing.sm} ${spacing.xl};
  border: 1px solid
    ${(props) =>
      props.darkMode ? colors.neutral.medium : colors.neutral.medium};
  border-radius: 4px;
  background: ${(props) =>
    props.darkMode ? "transparent" : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  transition: all 0.3s ease;

  &:hover {
    border-color: ${colors.primary.main};
  }

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 2px ${(props) => `${colors.primary.main}20`};
  }
`;

const ClearButton = styled(motion.button)`
  position: absolute;
  right: ${spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: ${(props) =>
    props.darkMode ? colors.primary.main : colors.neutral.dark};
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active,
  &:hover,
  &:focus {
    transform: translateY(-50%);
  }
`;

const SelectionModeButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${spacing.sm} ${spacing.md};
  border-radius: 4px;
  border: none;
  background: ${(props) =>
    props.isActive
      ? colors.primary.main
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.light};
  color: ${(props) =>
    props.isActive
      ? colors.neutral.white
      : props.darkMode
      ? colors.neutral.white
      : colors.neutral.darkest};
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-right: ${spacing.sm};

  &:hover {
    background: ${(props) =>
      props.isActive
        ? colors.primary.dark
        : props.darkMode
        ? colors.neutral.medium
        : colors.neutral.medium};
  }
`;

const SortButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${spacing.sm} ${spacing.md};
  background: ${(props) =>
    props.darkMode ? colors.primary.dark : colors.primary.light};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.primary.dark};
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: ${spacing.md};
`;

const FilterButton = styled.button`
  padding: ${spacing.sm} ${spacing.md};
  border-radius: 4px;
  border: 1px solid
    ${(props) =>
      props.darkMode ? colors.neutral.medium : colors.neutral.light};
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 120px;

  &:hover {
    border-color: ${colors.primary.main};
  }
`;

const MobileFilterContainer = styled(motion.div)`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  padding: ${spacing.md};
  margin-bottom: ${spacing.md};
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  overflow: hidden;
`;

const DropdownItem = styled.button`
  display: block;
  width: 100%;
  padding: ${spacing.sm} ${spacing.md};
  text-align: left;
  background: none;
  border: none;
  border-bottom: 1px solid
    ${(props) =>
      props.darkMode ? colors.neutral.dark : colors.neutral.lighter};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  cursor: pointer;
  font-size: 14px;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: ${(props) =>
      props.darkMode ? colors.neutral.dark : colors.neutral.lighter};
  }

  ${(props) =>
    props.active &&
    `
    background: ${
      props.darkMode ? colors.primary.dark : colors.primary.lighter
    };
    color: ${props.darkMode ? colors.neutral.white : colors.primary.dark};
    font-weight: bold;
    
    &:hover {
      background: ${
        props.darkMode ? colors.primary.darker : colors.primary.light
      };
    }
  `}
`;

const FilterLabel = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  font-size: 14px;
  font-weight: 500;
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const DateFilterButton = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  padding: ${spacing.sm};
  border-radius: 4px;
  border: 1px solid
    ${(props) =>
      props.darkMode ? colors.neutral.medium : colors.neutral.light};
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  cursor: pointer;

  svg {
    margin-right: 4px;
  }
`;

const DateDropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  width: 150px;
  /* Make background more distinct in dark mode */
  background: ${(props) => (props.darkMode ? "#1a1a1a" : colors.neutral.white)};
  border: 1px solid
    ${(props) =>
      props.darkMode ? colors.neutral.medium : colors.neutral.light};
  border-radius: 4px;
  /* Add stronger shadow for better separation */
  box-shadow: ${(props) =>
    props.darkMode
      ? "0 4px 12px rgba(0, 0, 0, 0.5)"
      : "0 4px 8px rgba(0, 0, 0, 0.2)"};
  margin-top: 4px;
  /* Ensure full opacity */
  opacity: 1;
`;

const DateOption = styled.div`
  padding: ${spacing.sm};
  cursor: pointer;
  /* Ensure solid background colors */
  background: ${(props) =>
    props.selected
      ? props.darkMode
        ? colors.primary.dark
        : colors.primary.light
      : props.darkMode
      ? "#1a1a1a"
      : colors.neutral.white};
  color: ${(props) =>
    props.selected
      ? colors.neutral.white
      : props.darkMode
      ? colors.neutral.white
      : colors.neutral.darkest};

  &:hover {
    background: ${(props) =>
      props.darkMode ? colors.neutral.dark : colors.neutral.light};
  }

  transition: background-color 0.2s;
  user-select: none;

  &:active {
    background: ${(props) =>
      props.darkMode ? colors.primary.darker : colors.primary.dark};
    color: ${colors.neutral.white};
  }
`;

const MobileMenuContainer = styled.div`
  margin-bottom: ${spacing.md};
  display: none;

  @media (max-width: ${breakpoints.md}) {
    display: block;
  }
`;

const DesktopSidebar = styled.div`
  @media (max-width: ${breakpoints.md}) {
    display: ${(props) => (props.mobileDropdownOpen ? "block" : "none")};
  }
`;

const ContentArea = styled.div`
  width: 100%;

  @media (max-width: ${breakpoints.md}) {
    margin-top: ${(props) => (props.mobileDropdownOpen ? "0" : spacing.md)};
  }
`;

const FilterBadge = styled.span`
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${colors.primary.main};
  transform: translate(25%, -25%);
`;

const MessageManagerContainer = ({ darkMode }) => {
  // State management
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [expandedMessages, setExpandedMessages] = useState([]);
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    message: null,
  });

  // Add selection mode state
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState([]);

  // Add new state for dropdown menu
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [messagesPerPage, setMessagesPerPage] = useState(25);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Filter and sort state
  const [searchQuery, setSearchQuery] = useState("");
  const [dateFilter, setDateFilter] = useState("all");
  const [sortOrder, setSortOrder] = useState("newest");
  const [dateDropdownOpen, setDateDropdownOpen] = useState(false);

  // Fetch messages on component mount
  useEffect(() => {
    fetchMessages();
  }, []);

  // Add resize listener for responsive pagination
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      // Adjust messages per page based on screen size
      if (window.innerWidth < parseInt(breakpoints.md)) {
        setMessagesPerPage(10); // 10 messages on smaller screens
      } else {
        setMessagesPerPage(25); // 25 messages on larger screens
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Set initial value

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Reset to page 1 when changing tabs
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, searchQuery, dateFilter]);

  // Toggle message expansion
  const toggleMessageExpansion = (id) => {
    setExpandedMessages((prev) =>
      prev.includes(id) ? prev.filter((msgId) => msgId !== id) : [...prev, id]
    );
  };

  // Fetch messages from Firestore
  const fetchMessages = async () => {
    setLoading(true);
    try {
      const messagesList = await fetchMessagesFromDB();
      setMessages(messagesList);
    } catch (error) {
      console.error("Error fetching messages:", error);
    } finally {
      setLoading(false);
    }
  };

  // Handle toggling read status
  const handleToggleReadStatus = async (id) => {
    try {
      const updatedMessages = await toggleReadStatus(id, messages);
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error toggling read status:", error);
    }
  };

  // Handle toggling star status
  const handleToggleStar = async (id) => {
    try {
      const updatedMessages = await toggleStarStatus(id, messages);
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error toggling star status:", error);
    }
  };

  // Handle toggling archive status
  const handleToggleArchive = async (id) => {
    try {
      const updatedMessages = await toggleArchiveStatus(id, messages);
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error toggling archive status:", error);
    }
  };

  // Handle delete click
  const handleDeleteClick = (message) => {
    setDeleteModal({ isOpen: true, message });
  };

  // Handle delete by ID (for MessageList component)
  const handleDeleteById = (messageId) => {
    const message = messages.find((msg) => msg.id === messageId);
    if (message) {
      setDeleteModal({ isOpen: true, message });
    } else {
      console.error("Message not found for ID:", messageId);
    }
  };

  // Confirm delete message
  const confirmDeleteMessage = async () => {
    try {
      // Validate delete modal state
      if (!deleteModal.message) {
        console.error("No message selected for deletion");
        setDeleteModal({ isOpen: false, message: null });
        return;
      }

      // Check if it's a batch deletion
      if (deleteModal.message?.id === "batch") {
        if (!selectedMessages || selectedMessages.length === 0) {
          console.error("No messages selected for batch deletion");
          setDeleteModal({ isOpen: false, message: null });
          return;
        }
        await batchDeleteMessages(selectedMessages);
        setMessages(
          messages.filter((msg) => !selectedMessages.includes(msg.id))
        );
        setSelectedMessages([]);
        setIsSelectionMode(false);
      } else {
        // Single message deletion
        const messageId = deleteModal.message?.id;

        if (!messageId || typeof messageId !== "string") {
          console.error("Invalid message ID:", messageId);
          setDeleteModal({ isOpen: false, message: null });
          return;
        }

        const success = await deleteMessage(messageId);
        if (success) {
          setMessages(messages.filter((msg) => msg.id !== messageId));
        }
      }
      setDeleteModal({ isOpen: false, message: null });
    } catch (error) {
      console.error("Error deleting message:", error);
      setDeleteModal({ isOpen: false, message: null });
    }
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);

    // Force a reflow to ensure the transition works properly
    setTimeout(() => {
      window.dispatchEvent(new Event("resize"));
    }, 300);
  };

  // Get filtered messages
  const filteredMessages = getFilteredMessages(
    messages,
    activeTab,
    searchQuery,
    dateFilter,
    sortOrder
  );

  // Pagination logic
  const indexOfLastMessage = currentPage * messagesPerPage;
  const indexOfFirstMessage = indexOfLastMessage - messagesPerPage;
  const currentMessages = filteredMessages.slice(
    indexOfFirstMessage,
    indexOfLastMessage
  );
  const totalPages = Math.ceil(filteredMessages.length / messagesPerPage);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Collapse all expanded messages when changing page
    setExpandedMessages([]);
    // Scroll to top of message list
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // Handle search change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
  };

  // Calculate all the counts needed
  const unreadCount = messages.filter(
    (msg) => !msg.isRead && !msg.isArchived
  ).length;
  const readCount = messages.filter(
    (msg) => msg.isRead && !msg.isArchived
  ).length;
  const starredCount = messages.filter(
    (msg) => msg.isStarred && !msg.isArchived
  ).length;
  const archivedCount = messages.filter((msg) => msg.isArchived).length;
  const allMessagesCount = messages.filter((msg) => !msg.isArchived).length;

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "newest" ? "oldest" : "newest");
  };

  const handleCustomInputChange = (e) => {
    const text = e.target.textContent;
    setSearchQuery(text);
  };

  const toggleDateDropdown = () => {
    console.log("Toggling dropdown, current state:", !dateDropdownOpen);
    setDateDropdownOpen(!dateDropdownOpen);
  };

  useEffect(() => {
    // This is a hack to force the select dropdown to use dark mode colors
    if (darkMode) {
      // Create a style element
      const style = document.createElement("style");
      // Add CSS to override the dropdown background
      style.textContent = `
        select, select option {
          background-color: #333333 !important;
          color: white !important;
          opacity: 1 !important;
        }
        
        /* Target Firefox */
        @-moz-document url-prefix() {
          select {
            background-color: #333333 !important;
            color: white !important;
          }
        }
        
        /* Target Chrome/Safari */
        select option {
          background-color: #333333 !important;
          color: white !important;
        }
        
        /* Target IE/Edge */
        select::-ms-expand {
          background-color: #333333 !important;
          color: white !important;
        }
      `;
      // Add the style to the document head
      document.head.appendChild(style);

      // Clean up function
      return () => {
        document.head.removeChild(style);
      };
    }
  }, [darkMode]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      // Only close if clicking outside the dropdown area
      const dateFilterWrapper = document.querySelector(".date-filter-wrapper");
      if (
        dateDropdownOpen &&
        dateFilterWrapper &&
        !dateFilterWrapper.contains(event.target)
      ) {
        setDateDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dateDropdownOpen]);

  // Add this useEffect to ensure the body has the dark-mode class
  useEffect(() => {
    if (darkMode) {
      document.body.classList.add("dark-mode");
    } else {
      document.body.classList.remove("dark-mode");
    }
  }, [darkMode]);

  // Add click outside handler to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Toggle dropdown menu
  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  // Add these functions to handle batch operations
  const toggleMessageSelection = (id) => {
    if (selectedMessages.includes(id)) {
      setSelectedMessages(
        selectedMessages.filter((messageId) => messageId !== id)
      );
      if (selectedMessages.length === 1) {
        setIsSelectionMode(false);
      }
    } else {
      setSelectedMessages([...selectedMessages, id]);
      if (!isSelectionMode) {
        setIsSelectionMode(true);
      }
    }
  };

  const toggleSelectionMode = () => {
    if (isSelectionMode) {
      setSelectedMessages([]);
    }
    setIsSelectionMode(!isSelectionMode);
  };

  const selectAllMessages = () => {
    if (selectedMessages.length === currentMessages.length) {
      setSelectedMessages([]);
    } else {
      setSelectedMessages(currentMessages.map((msg) => msg.id));
    }
  };

  // Batch action handlers
  const handleBatchMarkRead = async () => {
    try {
      const updatedMessages = await batchToggleReadStatus(
        selectedMessages,
        true,
        messages
      );
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error in batch mark read:", error);
    }
  };

  const handleBatchMarkUnread = async () => {
    try {
      const updatedMessages = await batchToggleReadStatus(
        selectedMessages,
        false,
        messages
      );
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error in batch mark unread:", error);
    }
  };

  const handleBatchStar = async () => {
    try {
      const updatedMessages = await batchToggleStarStatus(
        selectedMessages,
        true,
        messages
      );
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error in batch star:", error);
    }
  };

  const handleBatchUnstar = async () => {
    try {
      const updatedMessages = await batchToggleStarStatus(
        selectedMessages,
        false,
        messages
      );
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error in batch unstar:", error);
    }
  };

  const handleBatchArchive = async () => {
    try {
      const updatedMessages = await batchToggleArchiveStatus(
        selectedMessages,
        true,
        messages
      );
      setMessages(updatedMessages);
      setSelectedMessages([]);
      setIsSelectionMode(false);
    } catch (error) {
      console.error("Error in batch archive:", error);
    }
  };

  const handleBatchUnarchive = async () => {
    try {
      const updatedMessages = await batchToggleArchiveStatus(
        selectedMessages,
        false,
        messages
      );
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error in batch unarchive:", error);
    }
  };

  const handleBatchDelete = async () => {
    try {
      await batchDeleteMessages(selectedMessages);
      setMessages(messages.filter((msg) => !selectedMessages.includes(msg.id)));
      setSelectedMessages([]);
      setIsSelectionMode(false);
    } catch (error) {
      console.error("Error in batch delete:", error);
    }
  };

  // Define animation variants similar to Blog.jsx
  const sidebarVariants = {
    open: {
      x: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
    closed: {
      x: "-100%",
      opacity: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  // Add an overlay for when the mobile sidebar is open
  const Overlay = styled(motion.div)`
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  `;

  // Add useEffect for keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Only process if not typing in an input field
      if (
        e.target.tagName === "INPUT" ||
        e.target.tagName === "TEXTAREA" ||
        e.target.isContentEditable
      ) {
        return;
      }

      // Get the key pressed (lowercase for consistency)
      const key = e.key.toLowerCase();

      // Handle different shortcuts
      switch (key) {
        case "r": // Mark as read
          if (isSelectionMode && selectedMessages.length > 0) {
            handleBatchMarkRead();
          } else if (expandedMessages.length === 1) {
            handleToggleReadStatus(expandedMessages[0], true);
          }
          break;

        case "u": // Mark as unread
          if (isSelectionMode && selectedMessages.length > 0) {
            handleBatchMarkUnread();
          } else if (expandedMessages.length === 1) {
            handleToggleReadStatus(expandedMessages[0], false);
          }
          break;

        case "s": // Toggle star
          if (isSelectionMode && selectedMessages.length > 0) {
            // Check if all selected messages are starred
            const allStarred = selectedMessages.every(
              (id) => messages.find((msg) => msg.id === id)?.isStarred
            );

            if (allStarred) {
              handleBatchUnstar();
            } else {
              handleBatchStar();
            }
          } else if (expandedMessages.length === 1) {
            handleToggleStar(expandedMessages[0]);
          }
          break;

        case "a": // Toggle archive
          if (isSelectionMode && selectedMessages.length > 0) {
            // Check if all selected messages are archived
            const allArchived = selectedMessages.every(
              (id) => messages.find((msg) => msg.id === id)?.isArchived
            );

            if (allArchived) {
              handleBatchUnarchive();
            } else {
              handleBatchArchive();
            }
          } else if (expandedMessages.length === 1) {
            handleToggleArchive(expandedMessages[0]);
          }
          break;

        case "d": // Delete (with confirmation)
          if (isSelectionMode && selectedMessages.length > 0) {
            setDeleteModal({
              isOpen: true,
              message: {
                id: "batch",
                name: `${selectedMessages.length} selected messages`,
              },
            });
          } else if (expandedMessages.length === 1) {
            const messageToDelete = messages.find(
              (msg) => msg.id === expandedMessages[0]
            );
            if (messageToDelete) {
              handleDeleteClick(messageToDelete);
            }
          }
          break;

        default:
          // No matching shortcut
          break;
      }
    };

    // Add event listener
    window.addEventListener("keydown", handleKeyDown);

    // Clean up
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [
    messages,
    selectedMessages,
    isSelectionMode,
    expandedMessages,
    handleToggleReadStatus,
    handleToggleStar,
    handleToggleArchive,
    handleDeleteClick,
    handleBatchMarkRead,
    handleBatchMarkUnread,
    handleBatchStar,
    handleBatchUnstar,
    handleBatchArchive,
    handleBatchUnarchive,
  ]);

  return (
    <>
      <GlobalSelectStyle />
      <Container darkMode={darkMode}>
        <SectionTitle darkMode={darkMode}>Customer Messages</SectionTitle>

        {/* Mobile toggle button */}
        <MobileMenuContainer>
          <MobileSidebarToggle
            onClick={toggleDropdown}
            darkMode={darkMode}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FaBars />
          </MobileSidebarToggle>
        </MobileMenuContainer>

        <MessageLayout
          sidebarOpen={sidebarOpen}
          mobileDropdownOpen={dropdownOpen}
        >
          {/* Sidebar - visible on desktop always, and on mobile when toggled */}
          <DesktopSidebar mobileDropdownOpen={dropdownOpen}>
            <MessageFilters
              darkMode={darkMode}
              sidebarOpen={sidebarOpen}
              setSidebarOpen={setSidebarOpen}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              searchQuery={searchQuery}
              handleSearchChange={handleSearchChange}
              clearSearch={clearSearch}
              dateFilter={dateFilter}
              setDateFilter={setDateFilter}
              sortOrder={sortOrder}
              setSortOrder={setSortOrder}
              unreadCount={unreadCount}
              readCount={readCount}
              starredCount={starredCount}
              archivedCount={archivedCount}
              allMessagesCount={allMessagesCount}
              showFilters={false}
            />
          </DesktopSidebar>

          {/* Content area */}
          <ContentArea mobileDropdownOpen={dropdownOpen}>
            {/* Show batch actions when in selection mode and messages are selected */}
            <AnimatePresence>
              {isSelectionMode && selectedMessages.length > 0 && (
                <BatchActions
                  darkMode={darkMode}
                  selectedCount={selectedMessages.length}
                  onMarkRead={() => handleBatchMarkRead()}
                  onMarkUnread={() => handleBatchMarkUnread()}
                  onStar={() => handleBatchStar()}
                  onUnstar={() => handleBatchUnstar()}
                  onArchive={() => handleBatchArchive()}
                  onUnarchive={() => handleBatchUnarchive()}
                  onDelete={() => handleBatchDelete()}
                  selectedMessages={selectedMessages}
                  messages={messages}
                />
              )}
            </AnimatePresence>

            <FilterControls>
              <SearchContainer darkMode={darkMode}>
                <SearchIcon>
                  <FaSearch />
                </SearchIcon>
                <SearchInput
                  darkMode={darkMode}
                  type="text"
                  placeholder="Search messages..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
                {searchQuery && (
                  <ClearButton darkMode={darkMode} onClick={clearSearch}>
                    <FaTimes />
                  </ClearButton>
                )}
              </SearchContainer>

              {/* Add selection mode toggle button with icon */}
              <SelectionModeButton
                darkMode={darkMode}
                onClick={toggleSelectionMode}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                isActive={isSelectionMode}
                title={
                  isSelectionMode ? "Exit Selection Mode" : "Select Messages"
                }
              >
                {isSelectionMode ? <FaCheckSquare /> : <FaRegSquare />}
              </SelectionModeButton>

              <DateFilterWrapper className="date-filter-wrapper">
                <DateFilterButton
                  darkMode={darkMode}
                  onClick={() => setDateDropdownOpen(!dateDropdownOpen)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FaCalendarAlt />
                  {dateFilter !== "all" && <FilterBadge darkMode={darkMode} />}
                </DateFilterButton>

                {dateDropdownOpen && (
                  <DateDropdownMenu darkMode={darkMode}>
                    <DateOption
                      darkMode={darkMode}
                      selected={dateFilter === "all"}
                      onClick={(e) => {
                        e.stopPropagation();
                        setDateFilter("all");
                        setDateDropdownOpen(false);
                      }}
                    >
                      All Time
                    </DateOption>
                    <DateOption
                      darkMode={darkMode}
                      selected={dateFilter === "today"}
                      onClick={(e) => {
                        e.stopPropagation();
                        setDateFilter("today");
                        setDateDropdownOpen(false);
                      }}
                    >
                      Today
                    </DateOption>
                    <DateOption
                      darkMode={darkMode}
                      selected={dateFilter === "week"}
                      onClick={(e) => {
                        e.stopPropagation();
                        setDateFilter("week");
                        setDateDropdownOpen(false);
                      }}
                    >
                      This Week
                    </DateOption>
                    <DateOption
                      darkMode={darkMode}
                      selected={dateFilter === "month"}
                      onClick={(e) => {
                        e.stopPropagation();
                        setDateFilter("month");
                        setDateDropdownOpen(false);
                      }}
                    >
                      This Month
                    </DateOption>
                  </DateDropdownMenu>
                )}

                <SortButton
                  darkMode={darkMode}
                  onClick={toggleSortOrder}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  title={
                    sortOrder === "newest" ? "Newest First" : "Oldest First"
                  }
                >
                  {sortOrder === "newest" ? (
                    <FaSortAmountDown />
                  ) : (
                    <FaSortAmountUp />
                  )}
                </SortButton>
              </DateFilterWrapper>
            </FilterControls>

            <MessageExport
              darkMode={darkMode}
              filteredMessages={filteredMessages}
            />

            {loading ? (
              <LoadingSpinner />
            ) : (
              <MessageList
                darkMode={darkMode}
                messages={currentMessages}
                expandedMessages={expandedMessages}
                toggleMessageExpansion={toggleMessageExpansion}
                handleToggleReadStatus={handleToggleReadStatus}
                handleToggleStar={handleToggleStar}
                handleToggleArchiveStatus={handleToggleArchive}
                handleDeleteMessage={handleDeleteById}
                currentPage={currentPage}
                totalPages={totalPages}
                handlePageChange={handlePageChange}
                selectedMessages={selectedMessages}
                toggleMessageSelection={toggleMessageSelection}
                isSelectionMode={isSelectionMode}
                searchQuery={searchQuery}
              />
            )}
          </ContentArea>
        </MessageLayout>

        {deleteModal.isOpen && (
          <DeleteConfirmationModal
            isOpen={deleteModal.isOpen}
            onClose={() => setDeleteModal({ isOpen: false, message: null })}
            onConfirm={confirmDeleteMessage}
            title="Delete Message"
            message={`Are you sure you want to delete this message from ${
              deleteModal.message?.name || "Unknown"
            }?`}
            darkMode={darkMode}
          />
        )}

        {/* Add the keyboard shortcuts help component */}
        <KeyboardShortcutsHelp darkMode={darkMode} />
      </Container>
    </>
  );
};

const SelectStyleOverride = styled.div`
  /* This is a wrapper that will contain our global style */
`;

const GlobalSelectStyle = createGlobalStyle`
  body.dark-mode select,
  body.dark-mode select option {
    background-color: #333333 !important;
    color: white !important;
  }
  
  body.dark-mode select:focus {
    outline-color: ${colors.primary.main} !important;
  }
  
  /* Force the dropdown background to be dark */
  @-moz-document url-prefix() {
    body.dark-mode select {
      background-color: #333333 !important;
      color: white !important;
    }
  }
  
  /* For Webkit browsers */
  body.dark-mode select option {
    background-color: #333333 !important;
    color: white !important;
  }
  
  /* For IE/Edge */
  body.dark-mode select::-ms-expand {
    background-color: #333333 !important;
    color: white !important;
  }
`;

export default MessageManagerContainer;
