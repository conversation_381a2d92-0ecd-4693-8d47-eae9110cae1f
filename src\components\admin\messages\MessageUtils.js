import { db } from "../../../firebase/config";
import {
  collection,
  doc,
  getDocs,
  updateDoc,
  deleteDoc,
} from "firebase/firestore";

// Format timestamp for display
export const formatReadTimestamp = (date) => {
  if (!date) return "";

  // Handle Firestore timestamp or Date object
  const dateObj = date.toDate ? date.toDate() : new Date(date);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(dateObj);
};

// Format message content with proper paragraph spacing
export const formatMessageContent = (content, searchTerm = "") => {
  if (!content) return "No message content";

  // Split by double newlines to create paragraphs
  const paragraphs = content.split(/\n\s*\n/);

  // Join with proper paragraph spacing
  const formattedContent = paragraphs.join("\n\n");

  // Apply highlighting if search term exists
  if (searchTerm) {
    return highlightSearchTerms(formattedContent, searchTerm);
  }

  return formattedContent;
};

// Add this new function to highlight search terms in content
export const highlightSearchTerms = (content, searchTerm) => {
  if (!content || !searchTerm) return content;

  const term = searchTerm.trim().toLowerCase();
  if (!term) return content;

  // Split search term into words for more precise highlighting
  const searchWords = term.split(/\s+/).filter((word) => word.length > 0);

  let highlightedContent = content;

  // Replace each occurrence with highlighted version
  searchWords.forEach((word) => {
    if (word.length < 2) return; // Skip very short words

    const regex = new RegExp(`(${word})`, "gi");
    highlightedContent = highlightedContent.replace(regex, "<mark>$1</mark>");
  });

  return highlightedContent;
};

// Fetch messages from Firestore
export const fetchMessagesFromDB = async () => {
  try {
    const messagesCollection = collection(db, "messages");
    const messagesSnapshot = await getDocs(messagesCollection);
    const messagesList = messagesSnapshot.docs.map((doc) => {
      const data = doc.data();
      // Handle date conversion safely
      let dateValue = data.date;
      let readAtValue = data.readAt;

      if (dateValue) {
        if (dateValue.toDate && typeof dateValue.toDate === "function") {
          dateValue = dateValue.toDate();
        } else if (typeof dateValue === "string") {
          dateValue = new Date(dateValue);
        }
      } else {
        dateValue = new Date();
      }

      // Handle readAt conversion
      if (readAtValue) {
        if (readAtValue.toDate && typeof readAtValue.toDate === "function") {
          readAtValue = readAtValue.toDate();
        } else if (typeof readAtValue === "string") {
          readAtValue = new Date(readAtValue);
        }
      }

      return {
        id: doc.id,
        ...data,
        isArchived: data.isArchived || false,
        date: dateValue,
        readAt: readAtValue || null,
      };
    });

    // Sort messages by date, newest first
    messagesList.sort((a, b) => b.date - a.date);

    return messagesList;
  } catch (error) {
    console.error("Error fetching messages:", error);
    throw error;
  }
};

// Toggle read status
export const toggleReadStatus = async (id, messages) => {
  try {
    const message = messages.find((msg) => msg.id === id);
    const newReadStatus = !message.isRead;

    // Create an update object with the read status
    const updateData = { isRead: newReadStatus };

    // Add a readAt timestamp if marking as read
    if (newReadStatus) {
      updateData.readAt = new Date();
    } else {
      // Remove the readAt field if marking as unread
      updateData.readAt = null;
    }

    const messageRef = doc(db, "messages", id);
    await updateDoc(messageRef, updateData);

    return messages.map((msg) =>
      msg.id === id
        ? {
            ...msg,
            isRead: newReadStatus,
            readAt: newReadStatus ? new Date() : null,
          }
        : msg
    );
  } catch (error) {
    console.error("Error toggling read status:", error);
    throw error;
  }
};

// Toggle star status
export const toggleStarStatus = async (id, messages) => {
  try {
    const message = messages.find((msg) => msg.id === id);
    const messageRef = doc(db, "messages", id);
    await updateDoc(messageRef, { isStarred: !message.isStarred });

    return messages.map((msg) =>
      msg.id === id ? { ...msg, isStarred: !msg.isStarred } : msg
    );
  } catch (error) {
    console.error("Error toggling star status:", error);
    throw error;
  }
};

// Toggle archive status
export const toggleArchiveStatus = async (id, messages) => {
  try {
    const message = messages.find((msg) => msg.id === id);
    const messageRef = doc(db, "messages", id);
    await updateDoc(messageRef, { isArchived: !message.isArchived });

    return messages.map((msg) =>
      msg.id === id ? { ...msg, isArchived: !msg.isArchived } : msg
    );
  } catch (error) {
    console.error("Error toggling archive status:", error);
    throw error;
  }
};

// Delete message
export const deleteMessage = async (id) => {
  try {
    // Validate input
    if (!id || typeof id !== "string") {
      console.error("Invalid message ID provided to deleteMessage:", id);
      throw new Error("Invalid message ID");
    }

    await deleteDoc(doc(db, "messages", id));
    return true;
  } catch (error) {
    console.error("Error deleting message:", error);
    throw error;
  }
};

// Batch operations
export const batchToggleReadStatus = async (ids, isRead, messages) => {
  try {
    const updatePromises = ids.map((id) => {
      const messageRef = doc(db, "messages", id);
      const updateData = {
        isRead: isRead,
        readAt: isRead ? new Date() : null,
      };
      return updateDoc(messageRef, updateData);
    });

    await Promise.all(updatePromises);

    return messages.map((msg) =>
      ids.includes(msg.id)
        ? { ...msg, isRead: isRead, readAt: isRead ? new Date() : null }
        : msg
    );
  } catch (error) {
    console.error("Error in batch toggle read status:", error);
    throw error;
  }
};

export const batchToggleStarStatus = async (ids, isStarred, messages) => {
  try {
    const updatePromises = ids.map((id) => {
      const messageRef = doc(db, "messages", id);
      return updateDoc(messageRef, { isStarred: isStarred });
    });

    await Promise.all(updatePromises);

    return messages.map((msg) =>
      ids.includes(msg.id) ? { ...msg, isStarred: isStarred } : msg
    );
  } catch (error) {
    console.error("Error in batch toggle star status:", error);
    throw error;
  }
};

export const batchToggleArchiveStatus = async (ids, isArchived, messages) => {
  try {
    const updatePromises = ids.map((id) => {
      const messageRef = doc(db, "messages", id);
      return updateDoc(messageRef, { isArchived: isArchived });
    });

    await Promise.all(updatePromises);

    return messages.map((msg) =>
      ids.includes(msg.id) ? { ...msg, isArchived: isArchived } : msg
    );
  } catch (error) {
    console.error("Error in batch toggle archive status:", error);
    throw error;
  }
};

export const batchDeleteMessages = async (ids) => {
  try {
    // Validate input
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      console.warn("Invalid ids provided to batchDeleteMessages:", ids);
      return false;
    }

    const deletePromises = ids.map((id) => {
      if (!id) {
        console.warn("Invalid id in batch delete:", id);
        return Promise.resolve();
      }
      return deleteDoc(doc(db, "messages", id));
    });

    await Promise.all(deletePromises);
    return true;
  } catch (error) {
    console.error("Error in batch delete messages:", error);
    throw error;
  }
};

// Filter messages based on active tab, search, and date filter
export const getFilteredMessages = (
  messages,
  activeTab,
  searchTerm,
  dateFilter,
  sortOrder
) => {
  // First filter by tab
  let filtered = messages;

  switch (activeTab) {
    case "unread":
      filtered = messages.filter((msg) => !msg.isRead && !msg.isArchived);
      break;
    case "read":
      filtered = messages.filter((msg) => msg.isRead && !msg.isArchived);
      break;
    case "starred":
      filtered = messages.filter((msg) => msg.isStarred && !msg.isArchived);
      break;
    case "archived":
      filtered = messages.filter((msg) => msg.isArchived);
      break;
    case "all":
    default:
      filtered = messages.filter((msg) => !msg.isArchived);
      break;
  }

  // Then filter by search term
  if (searchTerm) {
    const term = searchTerm.toLowerCase();
    filtered = filtered.filter(
      (msg) =>
        (msg.subject && msg.subject.toLowerCase().includes(term)) ||
        (msg.message && msg.message.toLowerCase().includes(term)) ||
        (msg.name && msg.name.toLowerCase().includes(term)) ||
        (msg.email && msg.email.toLowerCase().includes(term))
    );
  }

  // Then filter by date
  if (dateFilter !== "all") {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today);
    weekAgo.setDate(weekAgo.getDate() - 7);
    const monthAgo = new Date(today);
    monthAgo.setMonth(monthAgo.getMonth() - 1);

    filtered = filtered.filter((msg) => {
      const msgDate = new Date(msg.date);
      switch (dateFilter) {
        case "today":
          return msgDate >= today;
        case "week":
          return msgDate >= weekAgo;
        case "month":
          return msgDate >= monthAgo;
        default:
          return true;
      }
    });
  }

  // Finally sort by date
  return [...filtered].sort((a, b) => {
    if (sortOrder === "newest") {
      return new Date(b.date) - new Date(a.date);
    } else {
      return new Date(a.date) - new Date(b.date);
    }
  });
};
