<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Form</title>
    <script src="https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.8.0/firebase-functions.js"></script>
</head>
<body>
    <h1>Test Contact Form</h1>
    <form id="testForm">
        <div>
            <label>Name:</label>
            <input type="text" id="name" value="Test User" required>
        </div>
        <div>
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Subject:</label>
            <input type="text" id="subject" value="Test Subject" required>
        </div>
        <div>
            <label>Message:</label>
            <textarea id="message" required>This is a test message.</textarea>
        </div>
        <button type="submit">Send Test</button>
    </form>

    <div id="result"></div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ",
            authDomain: "skinglow1000.firebaseapp.com",
            projectId: "skinglow1000",
            storageBucket: "skinglow1000.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdefghijklmnop"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const functions = firebase.functions();

        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                subject: document.getElementById('subject').value,
                message: document.getElementById('message').value
            };

            console.log('Sending data:', formData);
            document.getElementById('result').innerHTML = 'Sending...';

            try {
                const sendContactFormReply = firebase.functions().httpsCallable('sendContactFormReply');
                const result = await sendContactFormReply(formData);
                
                console.log('Success:', result);
                document.getElementById('result').innerHTML = `
                    <h3>Success!</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `
                    <h3>Error!</h3>
                    <p><strong>Code:</strong> ${error.code}</p>
                    <p><strong>Message:</strong> ${error.message}</p>
                    <pre>${JSON.stringify(error, null, 2)}</pre>
                `;
            }
        });
    </script>
</body>
</html>
