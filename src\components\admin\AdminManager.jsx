import { useState, useEffect } from "react";
import { httpsCallable } from "firebase/functions";
import { functions } from "../../firebase/config";
import { useAuth } from "../../context/AuthContext";
import styled from "styled-components";
import { colors, spacing, typography, breakpoints } from "../../styles";
import {
  FaUserShield,
  FaUserEdit,
  FaUserTie,
  FaPlus,
  FaTrash,
  FaEye,
  FaInfoCircle,
  FaSearch,
  FaFilter,
} from "react-icons/fa";

const AdminManagerContainer = styled.div`
  padding: ${spacing.lg};
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: ${breakpoints.tablet}) {
    padding: ${spacing.md};
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.xl};
  flex-wrap: wrap;
  gap: ${spacing.md};

  h1 {
    color: ${(props) =>
      props.$darkMode ? colors.neutral.white : colors.neutral.dark};
    margin: 0;
    display: flex;
    align-items: center;
    gap: ${spacing.sm};
    font-size: ${typography.fontSize.xl};

    @media (max-width: ${breakpoints.md}) {
      font-size: ${typography.fontSize.lg};
    }
  }
`;

const AccessLevelsCard = styled.div`
  background: ${(props) =>
    props.$darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.lg};
  margin-bottom: ${spacing.xl};
  box-shadow: ${(props) =>
    props.$darkMode
      ? "0 4px 6px rgba(0, 0, 0, 0.3)"
      : "0 4px 6px rgba(0, 0, 0, 0.1)"};
  border: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.gray : colors.neutral.light)};
`;

const AccessLevelGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${spacing.lg};
  margin-top: ${spacing.lg};

  @media (max-width: ${breakpoints.md}) {
    grid-template-columns: 1fr;
  }
`;

const AccessLevel = styled.div`
  background: ${(props) => {
    if (props.$level === "admin")
      return props.$darkMode ? colors.primary.dark : colors.primary.light;
    if (props.$level === "editor")
      return props.$darkMode ? colors.secondary.dark : colors.secondary.light;
    return props.$darkMode ? colors.neutral.dark : colors.neutral.light;
  }};
  border-radius: 8px;
  padding: ${spacing.md};
  border: 2px solid
    ${(props) => {
      if (props.$level === "admin") return colors.primary.main;
      if (props.$level === "editor") return colors.secondary.main;
      return props.$darkMode ? colors.neutral.gray : colors.neutral.medium;
    }};

  h3 {
    color: ${(props) =>
      props.$darkMode ? colors.neutral.white : colors.neutral.dark};
    margin: 0 0 ${spacing.sm} 0;
    display: flex;
    align-items: center;
    gap: ${spacing.sm};
    font-size: ${typography.fontSize.md};
    font-weight: 600;
  }

  ul {
    margin: 0;
    padding-left: ${spacing.md};
    color: ${(props) =>
      props.$darkMode ? colors.neutral.lightest : colors.neutral.darkGray};
    font-size: ${typography.fontSize.sm};
    line-height: 1.5;

    li {
      margin-bottom: ${spacing.xs};
    }
  }
`;

const UserManagementSection = styled.div`
  background: ${(props) =>
    props.$darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.lg};
  box-shadow: ${(props) =>
    props.$darkMode
      ? "0 4px 6px rgba(0, 0, 0, 0.3)"
      : "0 4px 6px rgba(0, 0, 0, 0.1)"};
  border: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.gray : colors.neutral.light)};
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.lg};
  flex-wrap: wrap;
  gap: ${spacing.md};

  h2 {
    color: ${(props) =>
      props.$darkMode ? colors.neutral.white : colors.neutral.dark};
    margin: 0;
    font-size: ${typography.fontSize.lg};
  }
`;

const SearchAndFilter = styled.div`
  display: flex;
  gap: ${spacing.md};
  margin-bottom: ${spacing.lg};
  flex-wrap: wrap;

  @media (max-width: ${breakpoints.md}) {
    flex-direction: column;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 250px;
  padding: ${spacing.sm} ${spacing.md};
  border: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 8px;
  background: ${(props) =>
    props.$darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize.sm};

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 2px ${colors.primary.light};
  }

  &::placeholder {
    color: ${(props) =>
      props.$darkMode ? colors.neutral.gray : colors.neutral.medium};
  }
`;

const FilterSelect = styled.select`
  padding: ${spacing.sm} ${spacing.md};
  border: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 8px;
  background: ${(props) =>
    props.$darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize.sm};
  min-width: 150px;

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const AddUserForm = styled.form`
  display: flex;
  gap: ${spacing.md};
  margin-bottom: ${spacing.lg};
  padding: ${spacing.md};
  background: ${(props) =>
    props.$darkMode ? colors.neutral.dark : colors.neutral.light};
  border-radius: 8px;
  flex-wrap: wrap;
  border: 1px solid
    ${(props) =>
      props.$darkMode ? colors.neutral.gray : colors.neutral.medium};

  @media (max-width: ${breakpoints.md}) {
    flex-direction: column;
  }
`;

const FormInput = styled.input`
  flex: 1;
  min-width: 200px;
  padding: ${spacing.sm} ${spacing.md};
  border: 1px solid
    ${(props) =>
      props.$darkMode ? colors.neutral.gray : colors.neutral.medium};
  border-radius: 6px;
  background: ${(props) =>
    props.$darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize.sm};

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 2px ${colors.primary.light};
  }

  &::placeholder {
    color: ${(props) =>
      props.$darkMode ? colors.neutral.gray : colors.neutral.medium};
  }
`;

const FormSelect = styled.select`
  padding: ${spacing.sm} ${spacing.md};
  border: 1px solid
    ${(props) =>
      props.$darkMode ? colors.neutral.gray : colors.neutral.medium};
  border-radius: 6px;
  background: ${(props) =>
    props.$darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize.sm};
  min-width: 120px;

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 2px ${colors.primary.light};
  }
`;

const Button = styled.button`
  padding: ${spacing.sm} ${spacing.md};
  border: none;
  border-radius: 6px;
  font-size: ${typography.fontSize.sm};
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  transition: all 0.2s ease;
  white-space: nowrap;

  ${(props) => {
    if (props.$variant === "primary") {
      return `
        background: ${colors.primary.main};
        color: white;
        &:hover { background: ${colors.primary.dark}; }
      `;
    }
    if (props.$variant === "danger") {
      return `
        background: ${colors.error.main};
        color: white;
        &:hover { background: ${colors.error.dark}; }
      `;
    }
    return `
      background: ${
        props.$darkMode ? colors.neutral.medium : colors.neutral.light
      };
      color: ${props.$darkMode ? colors.neutral.white : colors.neutral.dark};
      border: 1px solid ${
        props.$darkMode ? colors.neutral.light : colors.neutral.medium
      };
      &:hover {
        background: ${
          props.$darkMode ? colors.neutral.light : colors.neutral.medium
        };
      }
    `;
  }}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const UsersGrid = styled.div`
  display: grid;
  gap: ${spacing.md};

  @media (min-width: ${breakpoints.md}) {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
`;

const UserCard = styled.div`
  background: ${(props) =>
    props.$darkMode ? colors.neutral.dark : colors.neutral.light};
  border-radius: 8px;
  padding: ${spacing.md};
  border: 1px solid
    ${(props) =>
      props.$darkMode ? colors.neutral.gray : colors.neutral.medium};
  transition: all 0.2s ease;

  &:hover {
    box-shadow: ${(props) =>
      props.$darkMode
        ? "0 4px 8px rgba(0, 0, 0, 0.3)"
        : "0 4px 8px rgba(0, 0, 0, 0.1)"};
    transform: translateY(-2px);
    border-color: ${(props) =>
      props.$darkMode ? colors.neutral.light : colors.primary.light};
  }
`;

const UserHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${spacing.sm};
  gap: ${spacing.sm};
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0;

  h4 {
    margin: 0 0 ${spacing.xs} 0;
    color: ${(props) =>
      props.$darkMode ? colors.neutral.white : colors.neutral.dark};
    font-size: ${typography.fontSize.md};
    font-weight: 600;
    word-break: break-word;
  }

  p {
    margin: 0;
    color: ${(props) =>
      props.$darkMode ? colors.neutral.lightest : colors.neutral.darkGray};
    font-size: ${typography.fontSize.sm};
    word-break: break-word;
  }
`;

const RoleBadges = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${spacing.xs};
  margin-top: ${spacing.sm};
`;

const RoleBadge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: ${spacing.xs};
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 12px;
  font-size: ${typography.fontSize.xs};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  ${(props) => {
    if (props.$role === "admin") {
      return `
        background: ${
          props.$darkMode ? colors.primary.dark : colors.primary.light
        };
        color: ${props.$darkMode ? colors.primary.light : colors.primary.dark};
        border: 1px solid ${colors.primary.main};
      `;
    }
    if (props.$role === "editor") {
      return `
        background: ${
          props.$darkMode ? colors.secondary.dark : colors.secondary.light
        };
        color: ${
          props.$darkMode ? colors.secondary.light : colors.secondary.dark
        };
        border: 1px solid ${colors.secondary.main};
      `;
    }
    return `
      background: ${
        props.$darkMode ? colors.neutral.darker : colors.neutral.white
      };
      color: ${props.$darkMode ? colors.neutral.lightest : colors.neutral.dark};
      border: 1px solid ${
        props.$darkMode ? colors.neutral.gray : colors.neutral.medium
      };
    `;
  }}
`;

const UserActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.xs};
  margin-top: ${spacing.sm};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${spacing.xl};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.light : colors.neutral.medium};

  h3 {
    margin: ${spacing.md} 0 ${spacing.sm} 0;
    color: ${(props) =>
      props.$darkMode ? colors.neutral.white : colors.neutral.dark};
  }
`;

// UsersList Component
const UsersList = ({
  users,
  darkMode,
  onRemoveRole,
  currentUserEmail,
  loading,
}) => {
  if (loading) {
    return null; // Loading is handled by parent
  }

  if (!users || users.length === 0) {
    return (
      <EmptyState $darkMode={darkMode}>
        <FaUserShield size={48} />
        <h3>No Users Found</h3>
        <p>
          No users with admin privileges found. Add users using the form above.
        </p>
      </EmptyState>
    );
  }

  const getRoleIcon = (role) => {
    switch (role) {
      case "admin":
        return FaUserShield;
      case "editor":
        return FaUserEdit;
      case "moderator":
        return FaUserTie;
      default:
        return FaUserTie;
    }
  };

  const getUserRoles = (user) => {
    const roles = [];
    const claims = user.customClaims || {};

    if (claims.admin) roles.push("admin");
    if (claims.editor) roles.push("editor");
    if (claims.moderator) roles.push("moderator");

    return roles;
  };

  return (
    <UsersGrid>
      {users.map((user) => {
        const userRoles = getUserRoles(user);
        const isCurrentUser = user.email === currentUserEmail;

        return (
          <UserCard key={user.uid} $darkMode={darkMode}>
            <UserHeader>
              <UserInfo $darkMode={darkMode}>
                <h4>{user.displayName || "No Name"}</h4>
                <p>{user.email}</p>
              </UserInfo>
            </UserHeader>

            <RoleBadges>
              {userRoles.map((role) => {
                const IconComponent = getRoleIcon(role);
                return (
                  <RoleBadge key={role} $role={role} $darkMode={darkMode}>
                    <IconComponent size={12} />
                    {role}
                  </RoleBadge>
                );
              })}
              {userRoles.length === 0 && (
                <RoleBadge $role="user" $darkMode={darkMode}>
                  <FaUserTie size={12} />
                  user
                </RoleBadge>
              )}
            </RoleBadges>

            <UserActions>
              {userRoles.map((role) => (
                <Button
                  key={`remove-${role}`}
                  $variant="danger"
                  size="sm"
                  onClick={() => onRemoveRole(user.email, role)}
                  disabled={isCurrentUser}
                  $darkMode={darkMode}
                  style={{
                    fontSize: "12px",
                    padding: `${spacing.xs} ${spacing.sm}`,
                  }}
                >
                  <FaTrash size={10} />
                  Remove {role}
                </Button>
              ))}
              {isCurrentUser && (
                <p
                  style={{
                    fontSize: "12px",
                    color: darkMode
                      ? colors.neutral.light
                      : colors.neutral.medium,
                    margin: `${spacing.xs} 0 0 0`,
                    fontStyle: "italic",
                  }}
                >
                  (You cannot remove your own privileges)
                </p>
              )}
            </UserActions>
          </UserCard>
        );
      })}
    </UsersGrid>
  );
};

const AdminManager = ({ darkMode }) => {
  const { currentUser, isAdmin, refreshUserClaims } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ text: "", type: "" });
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [newUserEmail, setNewUserEmail] = useState("");
  const [newUserRole, setNewUserRole] = useState("moderator");
  const [showAddForm, setShowAddForm] = useState(false);

  // Access level definitions
  const accessLevels = {
    admin: {
      icon: FaUserShield,
      title: "Administrator",
      permissions: [
        "Full system access",
        "Manage all users and roles",
        "Create, edit, delete all content",
        "Access all admin functions",
        "Manage orders and payments",
        "Send emails and notifications",
        "View analytics and reports",
      ],
    },
    editor: {
      icon: FaUserEdit,
      title: "Editor",
      permissions: [
        "Create and edit content",
        "Manage products and blog posts",
        "Moderate reviews and comments",
        "View order information",
        "Send customer emails",
        "Cannot manage user roles",
        "Cannot delete critical data",
      ],
    },
    moderator: {
      icon: FaUserTie,
      title: "Moderator",
      permissions: [
        "Moderate user content",
        "Review and approve comments",
        "Manage customer messages",
        "View basic analytics",
        "Cannot edit products",
        "Cannot manage orders",
        "Cannot send system emails",
      ],
    },
  };

  useEffect(() => {
    console.log(
      "AdminManager useEffect - isAdmin:",
      isAdmin,
      "currentUser:",
      currentUser?.email
    );
    if (isAdmin && currentUser) {
      fetchUsers();
    }
  }, [isAdmin, currentUser]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Ensure user is authenticated and refresh token if needed
      if (!currentUser) {
        throw new Error("User not authenticated");
      }

      console.log(
        "Fetching users - currentUser:",
        currentUser.email,
        "isAdmin:",
        isAdmin
      );

      // Refresh user claims to ensure we have the latest admin status
      const claims = await refreshUserClaims();
      console.log("Refreshed claims:", claims);

      // Get fresh ID token
      const idToken = await currentUser.getIdToken(true);
      console.log("Got fresh ID token, length:", idToken.length);

      // Wait a moment to ensure authentication is fully loaded
      await new Promise((resolve) => setTimeout(resolve, 100));

      const getAdminUsers = httpsCallable(functions, "getAdminUsers");
      const result = await getAdminUsers({});

      console.log("getAdminUsers result:", result.data);

      if (result.data.error) {
        throw new Error(result.data.error);
      }

      setUsers(result.data.admins || []);
      setMessage({ text: "", type: "" }); // Clear any previous errors
    } catch (error) {
      console.error("Error fetching users:", error);

      // If it's an authentication error, try to refresh and retry once
      if (
        error.code === "unauthenticated" ||
        error.message.includes("logged in")
      ) {
        console.log("Authentication error, attempting retry...");
        try {
          await currentUser.getIdToken(true);
          await new Promise((resolve) => setTimeout(resolve, 500));

          const getAdminUsers = httpsCallable(functions, "getAdminUsers");
          const retryResult = await getAdminUsers({});

          if (retryResult.data.error) {
            throw new Error(retryResult.data.error);
          }

          setUsers(retryResult.data.admins || []);
          setMessage({ text: "", type: "" });
          return;
        } catch (retryError) {
          console.error("Retry also failed:", retryError);
        }
      }

      setMessage({
        text: "Error fetching users: " + error.message,
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async (e) => {
    e.preventDefault();
    if (!newUserEmail.trim()) {
      setMessage({ text: "Please enter a valid email address", type: "error" });
      return;
    }

    setLoading(true);
    try {
      // Ensure user is authenticated and has admin privileges
      if (!currentUser) {
        throw new Error("User not authenticated");
      }

      if (!isAdmin) {
        throw new Error("Insufficient permissions. Admin role required.");
      }

      // Refresh user claims to ensure we have the latest admin status
      await refreshUserClaims();

      let functionName;
      switch (newUserRole) {
        case "admin":
          functionName = "addAdminRole";
          break;
        case "editor":
          functionName = "addEditorRole";
          break;
        case "moderator":
          functionName = "addModeratorRole";
          break;
        default:
          throw new Error("Invalid role selected");
      }

      // Wait a moment to ensure authentication is fully loaded
      await new Promise((resolve) => setTimeout(resolve, 100));

      console.log(`Calling ${functionName} for ${newUserEmail}`);
      const addRole = httpsCallable(functions, functionName);
      const result = await addRole({ email: newUserEmail });

      console.log("Function result:", result.data);

      if (result.data.error) {
        setMessage({ text: result.data.error, type: "error" });
      } else {
        setMessage({ text: result.data.message, type: "success" });
        setNewUserEmail("");
        setNewUserRole("moderator");
        setShowAddForm(false);
        await fetchUsers(); // Refresh the user list
      }
    } catch (error) {
      console.error("Error adding user role:", error);
      setMessage({
        text: "Error adding user role: " + error.message,
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveRole = async (email, role) => {
    if (email === currentUser?.email) {
      setMessage({
        text: "You cannot remove your own admin privileges",
        type: "error",
      });
      return;
    }

    if (
      !window.confirm(
        `Are you sure you want to remove ${role} privileges from ${email}?`
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      let functionName;
      switch (role) {
        case "admin":
          functionName = "removeAdminRole";
          break;
        case "editor":
          functionName = "removeEditorRole";
          break;
        case "moderator":
          functionName = "removeModeratorRole";
          break;
        default:
          throw new Error("Invalid role");
      }

      const removeRole = httpsCallable(functions, functionName);
      const result = await removeRole({ email });

      if (result.data.error) {
        setMessage({ text: result.data.error, type: "error" });
      } else {
        setMessage({ text: result.data.message, type: "success" });
        await fetchUsers(); // Refresh the user list
      }
    } catch (error) {
      console.error("Error removing user role:", error);
      setMessage({
        text: "Error removing user role: " + error.message,
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter users based on search term and role filter
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      !searchTerm ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.displayName &&
        user.displayName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesRole =
      roleFilter === "all" ||
      (user.customClaims && user.customClaims[roleFilter]);

    return matchesSearch && matchesRole;
  });

  if (!isAdmin) {
    return (
      <AdminManagerContainer>
        <div style={{ textAlign: "center", padding: spacing.xl }}>
          <FaUserShield size={48} color={colors.neutral.medium} />
          <h2
            style={{
              color: darkMode ? colors.neutral.white : colors.neutral.dark,
              marginTop: spacing.md,
            }}
          >
            Access Denied
          </h2>
          <p
            style={{
              color: darkMode ? colors.neutral.light : colors.neutral.medium,
            }}
          >
            Only administrators can access user management.
          </p>
          <div
            style={{
              marginTop: spacing.lg,
              fontSize: "12px",
              color: colors.neutral.gray,
            }}
          >
            <p>Debug Info:</p>
            <p>User: {currentUser?.email || "Not logged in"}</p>
            <p>Claims: {JSON.stringify(userClaims, null, 2)}</p>
            <p>isAdmin: {isAdmin.toString()}</p>
            {currentUser && (
              <button
                onClick={async () => {
                  try {
                    const initializeAdmin = httpsCallable(
                      functions,
                      "initializeAdminCallable"
                    );
                    const result = await initializeAdmin();
                    console.log("Admin setup result:", result.data);
                    alert(
                      "Admin setup attempted. Check console and refresh page."
                    );
                    window.location.reload();
                  } catch (error) {
                    console.error("Admin setup error:", error);
                    alert("Admin setup failed: " + error.message);
                  }
                }}
                style={{
                  marginTop: spacing.sm,
                  padding: `${spacing.xs} ${spacing.sm}`,
                  background: colors.primary.main,
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                Initialize Admin (Debug)
              </button>
            )}
            {currentUser && (
              <button
                onClick={async () => {
                  try {
                    const testAuth = httpsCallable(functions, "testAuth");
                    const result = await testAuth({});
                    console.log("Auth test result:", result.data);
                    alert(
                      "Auth test result: " +
                        JSON.stringify(result.data, null, 2)
                    );
                  } catch (error) {
                    console.error("Auth test error:", error);
                    alert("Auth test failed: " + error.message);
                  }
                }}
                style={{
                  marginTop: spacing.sm,
                  padding: `${spacing.xs} ${spacing.sm}`,
                  background: colors.secondary.main,
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                Test Auth (Debug)
              </button>
            )}
          </div>
        </div>
      </AdminManagerContainer>
    );
  }

  return (
    <AdminManagerContainer>
      <Header $darkMode={darkMode}>
        <h1>
          <FaUserShield />
          Admin Manager
        </h1>
      </Header>

      {/* Access Levels Information */}
      <AccessLevelsCard $darkMode={darkMode}>
        <h2
          style={{
            color: darkMode ? colors.neutral.white : colors.neutral.dark,
            margin: 0,
            display: "flex",
            alignItems: "center",
            gap: spacing.sm,
          }}
        >
          <FaInfoCircle />
          Access Levels & Permissions
        </h2>
        <AccessLevelGrid>
          {Object.entries(accessLevels).map(([level, config]) => {
            const IconComponent = config.icon;
            return (
              <AccessLevel key={level} $level={level} $darkMode={darkMode}>
                <h3>
                  <IconComponent />
                  {config.title}
                </h3>
                <ul>
                  {config.permissions.map((permission, index) => (
                    <li key={index}>{permission}</li>
                  ))}
                </ul>
              </AccessLevel>
            );
          })}
        </AccessLevelGrid>
      </AccessLevelsCard>

      {/* User Management Section */}
      <UserManagementSection $darkMode={darkMode}>
        <SectionHeader $darkMode={darkMode}>
          <h2>Manage Users</h2>
          <Button
            $variant="primary"
            onClick={() => setShowAddForm(!showAddForm)}
            $darkMode={darkMode}
          >
            <FaPlus />
            Add User
          </Button>
        </SectionHeader>

        {/* Add User Form */}
        {showAddForm && (
          <AddUserForm onSubmit={handleAddUser} $darkMode={darkMode}>
            <FormInput
              type="email"
              placeholder="Enter user email"
              value={newUserEmail}
              onChange={(e) => setNewUserEmail(e.target.value)}
              $darkMode={darkMode}
              required
            />
            <FormSelect
              value={newUserRole}
              onChange={(e) => setNewUserRole(e.target.value)}
              $darkMode={darkMode}
            >
              <option value="moderator">Moderator</option>
              <option value="editor">Editor</option>
              <option value="admin">Administrator</option>
            </FormSelect>
            <Button
              type="submit"
              $variant="primary"
              $darkMode={darkMode}
              disabled={loading}
            >
              <FaPlus />
              Add Role
            </Button>
            <Button
              type="button"
              onClick={() => {
                setShowAddForm(false);
                setNewUserEmail("");
                setNewUserRole("moderator");
              }}
              $darkMode={darkMode}
            >
              Cancel
            </Button>
          </AddUserForm>
        )}

        {/* Search and Filter */}
        <SearchAndFilter>
          <div style={{ position: "relative", flex: 1, minWidth: "250px" }}>
            <FaSearch
              style={{
                position: "absolute",
                left: spacing.sm,
                top: "50%",
                transform: "translateY(-50%)",
                color: darkMode ? colors.neutral.light : colors.neutral.medium,
              }}
            />
            <SearchInput
              type="text"
              placeholder="Search users by email or name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              $darkMode={darkMode}
              style={{ paddingLeft: "2.5rem" }}
            />
          </div>
          <FilterSelect
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            $darkMode={darkMode}
          >
            <option value="all">All Roles</option>
            <option value="admin">Administrators</option>
            <option value="editor">Editors</option>
            <option value="moderator">Moderators</option>
          </FilterSelect>
        </SearchAndFilter>

        {/* Message Display */}
        {message.text && (
          <div
            style={{
              padding: spacing.md,
              borderRadius: "6px",
              marginBottom: spacing.lg,
              background:
                message.type === "error"
                  ? colors.error.light
                  : colors.success.light,
              color:
                message.type === "error"
                  ? colors.error.dark
                  : colors.success.dark,
              border: `1px solid ${
                message.type === "error"
                  ? colors.error.main
                  : colors.success.main
              }`,
            }}
          >
            {message.text}
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div style={{ textAlign: "center", padding: spacing.xl }}>
            <p
              style={{
                color: darkMode ? colors.neutral.light : colors.neutral.medium,
              }}
            >
              Loading users...
            </p>
          </div>
        )}

        {/* Users List */}
        <UsersList
          users={filteredUsers}
          darkMode={darkMode}
          onRemoveRole={handleRemoveRole}
          currentUserEmail={currentUser?.email}
          loading={loading}
        />
      </UserManagementSection>
    </AdminManagerContainer>
  );
};

export default AdminManager;
