import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaPaperPlane,
  FaTimes,
  FaCheck,
  FaExclamationTriangle,
} from "react-icons/fa";
import { colors, spacing, breakpoints } from "../../styles";
import { useAuth } from "../../context/AuthContext";
import { httpsCallable } from "firebase/functions";
import { functions } from "../../firebase/config";

const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${spacing.md};
`;

const Modal = styled(motion.div)`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
`;

const Header = styled.div`
  padding: ${spacing.lg};
  border-bottom: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Title = styled.h2`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
`;

const CloseButton = styled(motion.button)`
  background: none;
  border: none;
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 1.5rem;
  cursor: pointer;
  padding: ${spacing.sm};
  border-radius: 8px;

  &:hover {
    background: ${(props) =>
      props.darkMode ? colors.neutral.dark : colors.neutral.lightest};
  }
`;

const Form = styled.form`
  padding: ${spacing.lg};
`;

const FormGroup = styled.div`
  margin-bottom: ${spacing.lg};
`;

const Label = styled.label`
  display: block;
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-weight: 500;
  margin-bottom: ${spacing.sm};
`;

const Input = styled.input`
  width: 100%;
  padding: ${spacing.md};
  border: 2px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  border-radius: 8px;
  background: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 3px ${colors.primary.main}20;
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: ${spacing.md};
  border: 2px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  border-radius: 8px;
  background: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  font-size: 1rem;
  min-height: 150px;
  resize: vertical;
  font-family: inherit;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 3px ${colors.primary.main}20;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${spacing.md};
  justify-content: flex-end;
  margin-top: ${spacing.xl};

  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
  }
`;

const Button = styled(motion.button)`
  padding: ${spacing.md} ${spacing.lg};
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  transition: all 0.3s ease;

  ${(props) =>
    props.variant === "primary"
      ? `
    background: linear-gradient(135deg, ${colors.primary.main}, ${colors.primary.dark});
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px ${colors.primary.main}40;
    }
    
    &:disabled {
      background: ${colors.neutral.light};
      color: ${colors.neutral.dark};
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  `
      : `
    background: ${
      props.darkMode ? colors.neutral.dark : colors.neutral.lightest
    };
    color: ${props.darkMode ? colors.neutral.light : colors.neutral.dark};
    
    &:hover {
      background: ${
        props.darkMode ? colors.neutral.darker : colors.neutral.light
      };
    }
  `}
`;

const StatusMessage = styled(motion.div)`
  padding: ${spacing.md};
  border-radius: 8px;
  margin-bottom: ${spacing.lg};
  display: flex;
  align-items: center;
  gap: ${spacing.sm};

  ${(props) =>
    props.type === "success"
      ? `
    background: ${colors.success.light}20;
    color: ${colors.success.dark};
    border: 1px solid ${colors.success.light};
  `
      : props.type === "error"
      ? `
    background: ${colors.error.light}20;
    color: ${colors.error.dark};
    border: 1px solid ${colors.error.light};
  `
      : `
    background: ${colors.warning.light}20;
    color: ${colors.warning.dark};
    border: 1px solid ${colors.warning.light};
  `}
`;

const EmailComposer = ({
  isOpen,
  onClose,
  darkMode,
  recipientEmail = "",
  orderReference = "",
  replyData = null,
}) => {
  const [formData, setFormData] = useState({
    to: recipientEmail,
    subject: "",
    message: "",
    orderReference: orderReference,
  });
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState({ message: "", type: "" });
  const { user } = useAuth();

  // Update form data when reply data changes
  useEffect(() => {
    if (replyData) {
      setFormData({
        to: replyData.to,
        subject: replyData.subject,
        message: replyData.originalEmail
          ? `\n\n--- Original Message ---\nFrom: ${
              replyData.originalEmail.to
            }\nSubject: ${replyData.originalEmail.subject}\nDate: ${new Date(
              replyData.originalEmail.timestamp
            ).toLocaleString()}\n\n${
              replyData.originalEmail.content || "No content available"
            }`
          : "",
        orderReference: replyData.originalEmail?.orderReference || "",
      });
    } else if (recipientEmail || orderReference) {
      setFormData({
        to: recipientEmail,
        subject: "",
        message: "",
        orderReference: orderReference,
      });
    }
  }, [replyData, recipientEmail, orderReference]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setStatus({ message: "", type: "" });

    try {
      const simpleEmailTest = httpsCallable(functions, "simpleEmailTest");
      const result = await simpleEmailTest({
        to: formData.to,
        subject: formData.subject,
        message: formData.message,
      });

      setStatus({
        message: result.data.message,
        type: "success",
      });

      // Reset form after successful send
      setTimeout(() => {
        setFormData({ to: "", subject: "", message: "", orderReference: "" });
        onClose();
      }, 2000);
    } catch (error) {
      console.error("Error sending email:", error);
      setStatus({
        message: error.message || "Failed to send email. Please try again.",
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({ to: "", subject: "", message: "", orderReference: "" });
    setStatus({ message: "", type: "" });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <Overlay
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={handleClose}
      >
        <Modal
          darkMode={darkMode}
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          <Header darkMode={darkMode}>
            <Title darkMode={darkMode}>Send Email</Title>
            <CloseButton
              darkMode={darkMode}
              onClick={handleClose}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FaTimes />
            </CloseButton>
          </Header>

          <Form onSubmit={handleSubmit}>
            <AnimatePresence>
              {status.message && (
                <StatusMessage
                  type={status.type}
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  {status.type === "success" ? (
                    <FaCheck />
                  ) : status.type === "error" ? (
                    <FaExclamationTriangle />
                  ) : null}
                  {status.message}
                </StatusMessage>
              )}
            </AnimatePresence>

            <FormGroup>
              <Label darkMode={darkMode}>To Email Address</Label>
              <Input
                type="email"
                name="to"
                value={formData.to}
                onChange={handleChange}
                required
                darkMode={darkMode}
                placeholder="<EMAIL>"
              />
            </FormGroup>

            {orderReference && (
              <FormGroup>
                <Label darkMode={darkMode}>Order Reference</Label>
                <Input
                  type="text"
                  name="orderReference"
                  value={formData.orderReference}
                  onChange={handleChange}
                  darkMode={darkMode}
                  placeholder="Order ID or reference"
                />
              </FormGroup>
            )}

            <FormGroup>
              <Label darkMode={darkMode}>Subject</Label>
              <Input
                type="text"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                required
                darkMode={darkMode}
                placeholder="Email subject"
              />
            </FormGroup>

            <FormGroup>
              <Label darkMode={darkMode}>Message</Label>
              <TextArea
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                darkMode={darkMode}
                placeholder="Type your message here..."
              />
            </FormGroup>

            <ButtonGroup>
              <Button
                type="button"
                variant="secondary"
                darkMode={darkMode}
                onClick={handleClose}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={loading}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {loading ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{
                        repeat: Infinity,
                        duration: 1,
                        ease: "linear",
                      }}
                    >
                      <FaPaperPlane />
                    </motion.div>
                    Sending...
                  </>
                ) : (
                  <>
                    <FaPaperPlane /> Send Email
                  </>
                )}
              </Button>
            </ButtonGroup>
          </Form>
        </Modal>
      </Overlay>
    </AnimatePresence>
  );
};

export default EmailComposer;
