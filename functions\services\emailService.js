const admin = require("firebase-admin");
const functions = require("firebase-functions");

// Lazy load SendGrid to avoid startup timeout
let sgMail = null;
let isInitialized = false;

const initializeSendGrid = () => {
  if (isInitialized) return true;

  try {
    // Use environment variables instead of functions.config() for v2
    const apiKey = process.env.SENDGRID_API_KEY;
    if (!apiKey) {
      console.warn("SendGrid API key not found in environment variables");
      return false;
    }

    // Extra lazy load SendGrid - only require when actually needed
    if (!sgMail) {
      try {
        sgMail = require("@sendgrid/mail");
      } catch (requireError) {
        console.error("Failed to require SendGrid:", requireError);
        return false;
      }
    }

    sgMail.setApiKey(apiKey);
    isInitialized = true;
    console.log("SendGrid initialized successfully");
    return true;
  } catch (error) {
    console.error("Error initializing SendGrid:", error);
    return false;
  }
};

// Email configuration from environment variables
const getEmailConfig = () => {
  return {
    fromEmail: process.env.EMAIL_FROM_EMAIL || "<EMAIL>",
    fromName: process.env.EMAIL_FROM_NAME || "Lumina",
    replyTo: process.env.EMAIL_REPLY_TO || "<EMAIL>",
    supportEmail: process.env.EMAIL_SUPPORT_EMAIL || "<EMAIL>",
  };
};

// Beautiful email template
const getEmailTemplate = (content, title, type = "general") => {
  const config = getEmailConfig();

  const headerColors = {
    general: "linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%)", // Pastel pink to pastel green
    order: "linear-gradient(135deg, #FFB6C1 0%, #F0E68C 100%)", // Pastel pink to light yellow
    support: "linear-gradient(135deg, #98FB98 0%, #E0E0E0 100%)", // Pastel green to light gray
    welcome: "linear-gradient(135deg, #FFB6C1 0%, #DDA0DD 100%)", // Pastel pink to plum
  };

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: 'Comic Sans MS', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #4A4A4A;
            background: linear-gradient(135deg, #FFF0F5 0%, #F0FFF0 100%);
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(255, 182, 193, 0.3);
            border: 3px solid #FFE4E1;
        }
        .header {
            background: ${headerColors[type] || headerColors.general};
            color: white;
            padding: 40px 30px;
            text-align: center;
            border-radius: 20px 20px 0 0;
        }
        .header h1 {
            font-size: 32px;
            font-weight: 600;
            margin: 0 0 8px 0;
            letter-spacing: 1px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .header p {
            font-size: 16px;
            margin: 0;
            opacity: 0.95;
        }
        .content { 
            padding: 40px 30px; 
            font-size: 16px;
            line-height: 1.7;
        }
        .content h2 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .content p {
            margin-bottom: 16px;
        }
        .order-details {
            background: linear-gradient(135deg, #FFF0F5 0%, #F0FFF0 100%);
            padding: 20px;
            border-radius: 16px;
            margin: 20px 0;
            border: 2px solid #FFB6C1;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }
        .footer {
            background: linear-gradient(135deg, #FFF0F5 0%, #F0FFF0 100%);
            padding: 30px;
            text-align: center;
            border-top: 2px solid #FFE4E1;
            border-radius: 0 0 20px 20px;
        }
        .footer p {
            color: #8B4B8B;
            font-size: 14px;
            margin: 8px 0;
        }
        .footer a {
            color: #FF69B4;
            text-decoration: none;
            font-weight: 600;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 700;
            margin: 20px 0;
            box-shadow: 0 6px 20px rgba(255, 182, 193, 0.4);
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        @media (max-width: 600px) {
            .container { margin: 10px; border-radius: 8px; }
            .header, .content, .footer { padding: 20px; }
            .header h1 { font-size: 24px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${config.fromName}</h1>
            <p>Premium Skincare & Beauty</p>
        </div>
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>Thank you for choosing ${config.fromName}</p>
            <p>Need help? Contact us at <a href="mailto:${
              config.supportEmail
            }">${config.supportEmail}</a></p>
            <p>© 2024 ${config.fromName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;
};

// Update email status on order (cost-effective approach)
const updateOrderEmailStatus = async (
  orderReference,
  emailType,
  status,
  error = null
) => {
  if (!orderReference) return;

  try {
    const orderRef = admin.firestore().collection("orders").doc(orderReference);
    const updateData = {
      [`emailHistory.${emailType}`]: {
        sent: status === "sent",
        failed: status === "failed",
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        ...(error && { error: error }),
      },
    };

    await orderRef.update(updateData);
    console.log(
      `Email status updated for order ${orderReference}: ${emailType} = ${status}`
    );
  } catch (error) {
    console.error("Error updating order email status:", error);
  }
};

// Log email to Firestore (only for critical failures or non-order emails)
const logEmail = async (emailData) => {
  try {
    // Only log non-order emails or critical failures
    if (emailData.type === "order" && emailData.status !== "failed") {
      console.log(
        "Skipping email log for successful order email (tracked on order)"
      );
      return;
    }

    // Clean the data to remove undefined values
    const cleanData = {};
    Object.keys(emailData).forEach((key) => {
      if (emailData[key] !== undefined) {
        cleanData[key] = emailData[key];
      }
    });

    await admin
      .firestore()
      .collection("email_logs")
      .add({
        ...cleanData,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        createdAt: new Date(),
      });
  } catch (error) {
    console.error("Error logging email:", error);
  }
};

// Helper function to determine email type from subject
const getEmailTypeFromSubject = (subject) => {
  if (
    subject.includes("Order Confirmation") ||
    subject.includes("Order Created")
  )
    return "confirmation";
  if (subject.includes("Shipped") || subject.includes("On the Way"))
    return "shipped";
  if (subject.includes("Delivered")) return "delivered";
  if (subject.includes("Refunded")) return "refunded";
  if (subject.includes("Cancelled")) return "cancelled";
  return "general";
};

// Send email function
const sendEmail = async (emailData) => {
  const { to, subject, content, type = "general", orderReference } = emailData;
  const config = getEmailConfig();

  // Create HTML content
  const htmlContent = getEmailTemplate(content, subject, type);

  // Prepare email message
  const msg = {
    to,
    from: {
      email: config.fromEmail,
      name: config.fromName,
    },
    replyTo: config.replyTo,
    subject,
    html: htmlContent,
    text: content.replace(/<[^>]*>/g, ""), // Strip HTML for text version
  };

  // Log email data
  const logData = {
    to,
    subject,
    content: content.substring(0, 500) + (content.length > 500 ? "..." : ""),
    type,
    orderReference,
    status: "pending",
  };

  try {
    // Initialize SendGrid if not already done
    const isReady = initializeSendGrid();

    if (!isReady) {
      // Fallback to logging if SendGrid is not available
      logData.status = "logged_only";
      logData.provider = "none";
      logData.note = "SendGrid not configured - email logged only";
      logData.htmlContent = htmlContent.substring(0, 1000) + "...";
      console.log("SendGrid not available. Email logged only:", to, subject);
      await logEmail(logData);
      return logData;
    }

    // Send email via SendGrid
    await sgMail.send(msg);

    // Update log data for successful send
    logData.status = "sent";
    logData.provider = "sendgrid";
    logData.note = "Email sent successfully via SendGrid";
    console.log("Email sent successfully to:", to, "Subject:", subject);

    // Update order email status if this is an order-related email
    if (orderReference && type === "order") {
      const emailType = getEmailTypeFromSubject(subject);
      await updateOrderEmailStatus(orderReference, emailType, "sent");
    }
  } catch (error) {
    console.error("Error sending email:", error);
    console.error("SendGrid error details:", {
      code: error.code,
      message: error.message,
      response: error.response?.body,
      headers: error.response?.headers,
    });

    // Update log data for failed send
    logData.status = "failed";
    logData.provider = "sendgrid";
    logData.note = `SendGrid error: ${error.message}`;
    logData.error = error.message;
    logData.errorDetails = error.response?.body;

    // Update order email status if this is an order-related email
    if (orderReference && type === "order") {
      const emailType = getEmailTypeFromSubject(subject);
      await updateOrderEmailStatus(
        orderReference,
        emailType,
        "failed",
        error.message
      );
    }
  }

  // Log the email attempt (will be skipped for successful order emails)
  await logEmail(logData);

  return logData;
};

// Predefined email templates
const emailTemplates = {
  orderConfirmation: (orderData) => {
    const { orderId, items, total, shipping } = orderData;
    const itemsList = items
      .map(
        (item) =>
          `<li>${item.name} x ${item.quantity} - $${(
            item.price * item.quantity
          ).toFixed(2)}</li>`
      )
      .join("");

    return {
      subject: `Order Confirmation - ${orderId}`,
      content: `
        <h2>Thank you for your order!</h2>
        <p>We've received your order and are preparing it for shipment.</p>
        
        <div class="order-details">
          <h3>Order Details</h3>
          <p><strong>Order ID:</strong> ${orderId}</p>
          <p><strong>Order Total:</strong> $${total.toFixed(2)}</p>
          
          <h4>Items Ordered:</h4>
          <ul>${itemsList}</ul>
          
          <h4>Shipping Address:</h4>
          <p>${shipping.name}<br>
          ${shipping.address}<br>
          ${shipping.city}, ${shipping.state} ${shipping.zipCode}</p>
        </div>
        
        <p>You'll receive a shipping confirmation email once your order is on its way.</p>
      `,
      type: "order",
    };
  },

  contactFormReply: (contactData) => {
    const { name, email, subject } = contactData;
    return {
      subject: `Thank you for contacting us - ${subject}`,
      content: `
        <h2>Thank you for reaching out!</h2>
        <p>Hi ${name},</p>
        <p>We've received your message and will get back to you within 24 hours.</p>
        <p>In the meantime, feel free to browse our latest products and skincare tips.</p>
        <p>Best regards,<br>The Lumina Team</p>
      `,
      type: "support",
    };
  },

  welcomeEmail: (userData) => {
    const { name, email } = userData;
    return {
      subject: "Welcome to Lumina!",
      content: `
        <h2>Welcome to Lumina, ${name}!</h2>
        <p>We're thrilled to have you join our community of skincare enthusiasts.</p>
        <p>As a new member, you'll enjoy:</p>
        <ul>
          <li>Exclusive access to new product launches</li>
          <li>Personalized skincare recommendations</li>
          <li>Special member-only discounts</li>
          <li>Expert skincare tips and tutorials</li>
        </ul>
        <p>Start exploring our premium collection today!</p>
        <a href="https://Lumina.com/shop" class="button">Shop Now</a>
      `,
      type: "welcome",
    };
  },

  orderStatusUpdate: (orderData) => {
    const { orderId, status, customerName, items, trackingNumber } = orderData;

    const statusMessages = {
      processing: {
        title: "Order Confirmed & Processing",
        message:
          "Your order has been confirmed and is now being prepared for shipment.",
        icon: "⏳",
      },
      shipped: {
        title: "Order Shipped",
        message:
          "Great news! Your order has been shipped and is on its way to you.",
        icon: "🚚",
      },
      delivered: {
        title: "Order Delivered",
        message:
          "Your order has been successfully delivered. We hope you love your new products!",
        icon: "📦",
      },
      cancelled: {
        title: "Order Cancelled",
        message:
          "Your order has been cancelled as requested. Any charges will be refunded within 3-5 business days.",
        icon: "❌",
      },
      refunded: {
        title: "Order Refunded",
        message:
          "Your refund has been processed and will appear in your account within 3-5 business days.",
        icon: "💰",
      },
    };

    const statusInfo = statusMessages[status] || statusMessages.processing;

    const itemsList = items
      .map(
        (item) => `
      <tr>
        <td style="padding: 8px; border-bottom: 1px solid #eee;">
          ${item.name}
        </td>
        <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">
          ${item.quantity}
        </td>
        <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">
          $${(item.price * item.quantity).toFixed(2)}
        </td>
      </tr>
    `
      )
      .join("");

    const trackingSection = trackingNumber
      ? `
      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="margin: 0 0 10px 0; color: #333;">Tracking Information</h3>
        <p style="margin: 0; font-size: 16px; font-weight: bold; color: #007bff;">
          Tracking Number: ${trackingNumber}
        </p>
        <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
          You can track your package using this number on our shipping partner's website.
        </p>
      </div>
    `
      : "";

    return {
      subject: `${statusInfo.icon} Order Update: ${statusInfo.title} - #${orderId}`,
      content: `
        <h2>${statusInfo.icon} ${statusInfo.title}</h2>
        <p>Hi ${customerName},</p>
        <p>${statusInfo.message}</p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 15px 0; color: #333;">Order Details</h3>
          <p style="margin: 0 0 10px 0;"><strong>Order ID:</strong> #${orderId}</p>
          <p style="margin: 0 0 15px 0;"><strong>Status:</strong> ${
            status.charAt(0).toUpperCase() + status.slice(1)
          }</p>

          <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
            <thead>
              <tr style="background: #e9ecef;">
                <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Product</th>
                <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Qty</th>
                <th style="padding: 12px 8px; text-align: right; border-bottom: 2px solid #dee2e6;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${itemsList}
            </tbody>
          </table>
        </div>

        ${trackingSection}

        <p>If you have any questions about your order, please don't hesitate to contact our customer support team.</p>

        <div style="margin-top: 30px;">
          <a href="https://Lumina.com/account/orders" class="button">View Order Details</a>
        </div>

        <p style="margin-top: 20px; font-size: 14px; color: #666;">
          Thank you for choosing Lumina!
        </p>
      `,
      type: "order",
    };
  },

  abandonedCartRecovery: (cartData) => {
    const { cartItems, userEmail } = cartData;

    const itemsList = cartItems
      .map(
        (item) => `
      <tr>
        <td style="padding: 12px; border-bottom: 1px solid #eee;">
          <div style="display: flex; align-items: center;">
            ${
              item.image
                ? `<img src="${item.image}" alt="${item.name}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; margin-right: 12px;">`
                : ""
            }
            <div>
              <h4 style="margin: 0 0 4px 0; font-size: 16px;">${item.name}</h4>
              <p style="margin: 0; color: #666; font-size: 14px;">Quantity: ${
                item.quantity
              }</p>
            </div>
          </div>
        </td>
        <td style="padding: 12px; border-bottom: 1px solid #eee; text-align: right; font-weight: bold;">
          $${(item.price * item.quantity).toFixed(2)}
        </td>
      </tr>
    `
      )
      .join("");

    const totalValue = cartItems.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    return {
      subject: "🛒 Don't forget your Lumina favorites!",
      content: `
        <h2>🛒 Your cart is waiting for you!</h2>
        <p>Hi there,</p>
        <p>We noticed you left some amazing skincare products in your cart. Don't let your perfect skincare routine slip away!</p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 15px 0; color: #333;">Your Saved Items</h3>

          <table style="width: 100%; border-collapse: collapse;">
            <tbody>
              ${itemsList}
            </tbody>
          </table>

          <div style="text-align: right; margin-top: 15px; padding-top: 15px; border-top: 2px solid #dee2e6;">
            <h3 style="margin: 0; color: #333;">Total: $${totalValue.toFixed(
              2
            )}</h3>
          </div>
        </div>

        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
          <h3 style="margin: 0 0 10px 0; color: white;">✨ Special Offer Just for You!</h3>
          <p style="margin: 0 0 15px 0; color: white; font-size: 16px;">Complete your purchase in the next 24 hours and get <strong>10% OFF</strong> your entire order!</p>
          <p style="margin: 0; color: white; font-size: 14px;">Use code: <strong>COMEBACK10</strong></p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://Lumina.com/cart" class="button" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
            Complete Your Purchase
          </a>
        </div>

        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4 style="margin: 0 0 10px 0; color: #333;">Why choose Lumina?</h4>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li>Premium, dermatologist-tested ingredients</li>
            <li>Free shipping on orders over $50</li>
            <li>30-day money-back guarantee</li>
            <li>Expert skincare support</li>
          </ul>
        </div>

        <p style="color: #666; font-size: 14px;">
          If you're no longer interested in these products, you can
          <a href="https://Lumina.com/cart" style="color: #667eea;">clear your cart</a>
          or <a href="https://Lumina.com/unsubscribe" style="color: #667eea;">unsubscribe from these emails</a>.
        </p>

        <p style="margin-top: 20px; font-size: 14px; color: #666;">
          Happy glowing!<br>
          The Lumina Team
        </p>
      `,
      type: "general",
    };
  },

  reviewRequest: (orderData) => {
    const { orderId, customerName, items, userEmail } = orderData;

    const itemsList = items
      .map(
        (item) => `
      <div style="display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #eee;">
        ${
          item.image
            ? `<img src="${item.image}" alt="${item.name}" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px; margin-right: 15px;">`
            : ""
        }
        <div style="flex: 1;">
          <h4 style="margin: 0 0 5px 0; font-size: 16px; color: #333;">${
            item.name
          }</h4>
          <p style="margin: 0; color: #666; font-size: 14px;">Quantity: ${
            item.quantity
          }</p>
          <div style="margin-top: 10px;">
            <a href="https://Lumina.com/product/${item.id}/review"
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      color: white;
                      padding: 8px 16px;
                      text-decoration: none;
                      border-radius: 20px;
                      font-size: 14px;
                      font-weight: bold;
                      display: inline-block;">
              ⭐ Review This Product
            </a>
          </div>
        </div>
      </div>
    `
      )
      .join("");

    return {
      subject: "⭐ How did you like your Lumina products?",
      content: `
        <h2>⭐ We'd love your feedback!</h2>
        <p>Hi ${customerName},</p>
        <p>We hope you're loving your recent Lumina purchase! Your experience matters to us, and we'd be thrilled to hear about your skincare journey.</p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 15px 0; color: #333;">Your Recent Order #${orderId}</h3>
          <div style="background: white; border-radius: 8px; overflow: hidden;">
            ${itemsList}
          </div>
        </div>

        <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
          <h3 style="margin: 0 0 10px 0; color: white;">🎁 Review Reward</h3>
          <p style="margin: 0 0 15px 0; color: white; font-size: 16px;">Leave a review and get <strong>15% OFF</strong> your next order!</p>
          <p style="margin: 0; color: white; font-size: 14px;">Plus, help other skincare enthusiasts discover their perfect products!</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://Lumina.com/account/orders/${orderId}"
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    display: inline-block;
                    font-size: 16px;">
            ⭐ Write Your Reviews
          </a>
        </div>

        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4 style="margin: 0 0 10px 0; color: #333;">Your review helps us:</h4>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li>Improve our products and services</li>
            <li>Help other customers make informed decisions</li>
            <li>Build a community of skincare enthusiasts</li>
            <li>Celebrate your skincare success stories</li>
          </ul>
        </div>

        <p style="color: #666; font-size: 14px;">
          Reviews can be written within 60 days of delivery. If you need any assistance with your products,
          please don't hesitate to <a href="https://Lumina.com/contact" style="color: #667eea;">contact our support team</a>.
        </p>

        <p style="margin-top: 20px; font-size: 14px; color: #666;">
          Thank you for choosing Lumina!<br>
          The Lumina Team
        </p>
      `,
      type: "general",
    };
  },

  lowStockAlert: (alertData) => {
    const { products } = alertData;

    const productsList = products
      .map(
        (product) => `
      <tr>
        <td style="padding: 12px; border-bottom: 1px solid #eee;">
          <strong>${product.name}</strong><br>
          <small>ID: ${product.id}</small>
        </td>
        <td style="padding: 12px; border-bottom: 1px solid #eee; text-align: center;">
          <span style="color: ${
            product.stock <= 5 ? "#dc3545" : "#ffc107"
          }; font-weight: bold;">
            ${product.stock}
          </span>
        </td>
        <td style="padding: 12px; border-bottom: 1px solid #eee; text-align: center;">
          ${product.threshold}
        </td>
      </tr>
    `
      )
      .join("");

    return {
      subject: "🚨 Low Stock Alert - Immediate Action Required",
      content: `
        <h2>🚨 Low Stock Alert</h2>
        <p>The following products are running low on inventory and need immediate attention:</p>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 10px 0; color: #856404;">⚠️ Action Required</h3>
          <p style="margin: 0; color: #856404;">These products may go out of stock soon. Consider restocking or updating inventory levels.</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
          <thead>
            <tr style="background: #f8f9fa;">
              <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">Product</th>
              <th style="padding: 12px; text-align: center; border-bottom: 2px solid #dee2e6;">Current Stock</th>
              <th style="padding: 12px; text-align: center; border-bottom: 2px solid #dee2e6;">Threshold</th>
            </tr>
          </thead>
          <tbody>
            ${productsList}
          </tbody>
        </table>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://Lumina.com/admin/products"
             style="background: #dc3545;
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    display: inline-block;">
            Manage Inventory
          </a>
        </div>

        <p style="color: #666; font-size: 14px;">
          This alert is sent daily when products fall below their stock thresholds.
          You can adjust thresholds in the product management panel.
        </p>
      `,
      type: "general",
    };
  },

  restockNotification: (restockData) => {
    const { productId, productName, productImage, userEmail } = restockData;

    return {
      subject: "🎉 Great News! Your Favorite Product is Back in Stock",
      content: `
        <h2>🎉 It's back in stock!</h2>
        <p>Great news! The product you've been waiting for is now available again.</p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
          ${
            productImage
              ? `<img src="${productImage}" alt="${productName}" style="width: 150px; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 15px;">`
              : ""
          }
          <h3 style="margin: 0 0 10px 0; color: #333;">${productName}</h3>
          <p style="margin: 0; color: #666;">Now available for purchase!</p>
        </div>

        <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
          <h3 style="margin: 0 0 10px 0; color: white;">⚡ Limited Time Offer</h3>
          <p style="margin: 0 0 15px 0; color: white; font-size: 16px;">Get <strong>20% OFF</strong> this restocked item!</p>
          <p style="margin: 0; color: white; font-size: 14px;">Use code: <strong>BACKSTOCK20</strong></p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://Lumina.com/product/${productId}"
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    display: inline-block;
                    font-size: 16px;">
            🛒 Shop Now
          </a>
        </div>

        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4 style="margin: 0 0 10px 0; color: #333;">Why act fast?</h4>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li>Limited quantities available</li>
            <li>High demand product</li>
            <li>Exclusive discount for waitlist members</li>
            <li>Free shipping on orders over $50</li>
          </ul>
        </div>

        <p style="color: #666; font-size: 14px;">
          You received this email because you signed up for restock notifications.
          <a href="https://Lumina.com/unsubscribe" style="color: #667eea;">Unsubscribe from restock alerts</a>.
        </p>

        <p style="margin-top: 20px; font-size: 14px; color: #666;">
          Happy shopping!<br>
          The Lumina Team
        </p>
      `,
      type: "general",
    };
  },

  pointsEarned: (pointsData) => {
    const { userEmail, pointsEarned, totalPoints, tier, reason } = pointsData;

    const tierColors = {
      Bronze: "#cd7f32",
      Silver: "#c0c0c0",
      Gold: "#ffd700",
      Platinum: "#e5e4e2",
    };

    const tierBenefits = {
      Bronze: [
        "1 point per $1 spent",
        "Birthday discount",
        "Early sale access",
      ],
      Silver: [
        "1.25 points per $1 spent",
        "Free shipping",
        "Birthday discount",
        "Early sale access",
      ],
      Gold: [
        "1.5 points per $1 spent",
        "Free shipping",
        "Exclusive products",
        "Birthday discount",
        "Priority support",
      ],
      Platinum: [
        "2 points per $1 spent",
        "Free shipping",
        "Exclusive products",
        "Personal skincare consultant",
        "VIP events",
      ],
    };

    return {
      subject: `🎉 You earned ${pointsEarned} Lumina points!`,
      content: `
        <h2>🎉 Points Earned!</h2>
        <p>Congratulations! You've just earned <strong>${pointsEarned} points</strong> for: ${reason}</p>

        <div style="background: linear-gradient(135deg, ${
          tierColors[tier]
        } 0%, ${
        tierColors[tier]
      }aa 100%); padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; color: white;">
          <h3 style="margin: 0 0 10px 0;">Your ${tier} Status</h3>
          <p style="margin: 0; font-size: 24px; font-weight: bold;">${totalPoints} Points</p>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h4 style="margin: 0 0 15px 0; color: #333;">Your ${tier} Benefits:</h4>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            ${tierBenefits[tier]
              .map((benefit) => `<li>${benefit}</li>`)
              .join("")}
          </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://Lumina.com/account/loyalty"
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    display: inline-block;">
            View Your Rewards
          </a>
        </div>

        <p style="color: #666; font-size: 14px;">
          Keep earning points with every purchase and unlock even more exclusive benefits!
        </p>
      `,
      type: "general",
    };
  },

  tierUpgrade: (upgradeData) => {
    const { userEmail, newTier, previousTier, totalPoints } = upgradeData;

    const tierColors = {
      Bronze: "#cd7f32",
      Silver: "#c0c0c0",
      Gold: "#ffd700",
      Platinum: "#e5e4e2",
    };

    const tierEmojis = {
      Bronze: "🥉",
      Silver: "🥈",
      Gold: "🥇",
      Platinum: "💎",
    };

    return {
      subject: `🎊 Congratulations! You've been upgraded to ${newTier} status!`,
      content: `
        <h2>🎊 Tier Upgrade Celebration!</h2>
        <p>Amazing news! You've been upgraded from ${previousTier} to <strong>${newTier}</strong> status!</p>

        <div style="background: linear-gradient(135deg, ${
          tierColors[newTier]
        } 0%, ${
        tierColors[newTier]
      }aa 100%); padding: 30px; border-radius: 8px; margin: 20px 0; text-align: center; color: white;">
          <div style="font-size: 48px; margin-bottom: 10px;">${
            tierEmojis[newTier]
          }</div>
          <h3 style="margin: 0 0 10px 0; font-size: 28px;">${newTier} Member</h3>
          <p style="margin: 0; font-size: 18px;">${totalPoints} Total Points Earned</p>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h4 style="margin: 0 0 15px 0; color: #333;">🎁 Your New ${newTier} Benefits:</h4>
          ${
            newTier === "Silver"
              ? `
            <ul style="margin: 0; padding-left: 20px; color: #666;">
              <li><strong>1.25 points per $1 spent</strong> (upgraded from 1 point)</li>
              <li><strong>Free shipping</strong> on all orders</li>
              <li>Birthday discount</li>
              <li>Early sale access</li>
            </ul>
          `
              : newTier === "Gold"
              ? `
            <ul style="margin: 0; padding-left: 20px; color: #666;">
              <li><strong>1.5 points per $1 spent</strong> (upgraded from 1.25 points)</li>
              <li>Free shipping on all orders</li>
              <li><strong>Exclusive products</strong> access</li>
              <li>Birthday discount</li>
              <li><strong>Priority customer support</strong></li>
            </ul>
          `
              : `
            <ul style="margin: 0; padding-left: 20px; color: #666;">
              <li><strong>2 points per $1 spent</strong> (upgraded from 1.5 points)</li>
              <li>Free shipping on all orders</li>
              <li>Exclusive products access</li>
              <li><strong>Personal skincare consultant</strong></li>
              <li><strong>VIP events and experiences</strong></li>
            </ul>
          `
          }
        </div>

        <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
          <h3 style="margin: 0 0 10px 0; color: white;">🎁 Upgrade Bonus!</h3>
          <p style="margin: 0 0 15px 0; color: white; font-size: 16px;">Enjoy <strong>25% OFF</strong> your next order to celebrate!</p>
          <p style="margin: 0; color: white; font-size: 14px;">Use code: <strong>UPGRADE25</strong></p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://Lumina.com/shop"
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    display: inline-block;">
            Start Shopping
          </a>
        </div>

        <p style="color: #666; font-size: 14px;">
          Thank you for being a loyal Lumina customer! We're excited to provide you with even better benefits.
        </p>
      `,
      type: "general",
    };
  },

  monthlyLoyaltySummary: (summaryData) => {
    const { userEmail, currentPoints, tier, monthlyPointsEarned, totalEarned } =
      summaryData;

    const tierColors = {
      Bronze: "#cd7f32",
      Silver: "#c0c0c0",
      Gold: "#ffd700",
      Platinum: "#e5e4e2",
    };

    const nextTierPoints = {
      Bronze: 500,
      Silver: 2000,
      Gold: 5000,
      Platinum: null,
    };

    const pointsToNext = nextTierPoints[tier]
      ? nextTierPoints[tier] - totalEarned
      : 0;

    return {
      subject: `📊 Your Monthly Lumina Loyalty Summary - ${tier} Member`,
      content: `
        <h2>📊 Your Monthly Loyalty Summary</h2>
        <p>Here's a look at your Lumina loyalty activity this month!</p>

        <div style="background: linear-gradient(135deg, ${
          tierColors[tier]
        } 0%, ${
        tierColors[tier]
      }aa 100%); padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; color: white;">
          <h3 style="margin: 0 0 10px 0;">Current Status: ${tier}</h3>
          <p style="margin: 0; font-size: 24px; font-weight: bold;">${currentPoints} Available Points</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
            <h4 style="margin: 0 0 5px 0; color: #333;">This Month</h4>
            <p style="margin: 0; font-size: 20px; font-weight: bold; color: #28a745;">+${monthlyPointsEarned}</p>
            <p style="margin: 0; font-size: 14px; color: #666;">Points Earned</p>
          </div>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
            <h4 style="margin: 0 0 5px 0; color: #333;">Lifetime</h4>
            <p style="margin: 0; font-size: 20px; font-weight: bold; color: #667eea;">${totalEarned}</p>
            <p style="margin: 0; font-size: 14px; color: #666;">Total Earned</p>
          </div>
        </div>

        ${
          pointsToNext > 0
            ? `
          <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #1976d2;">🎯 Next Tier Goal</h4>
            <p style="margin: 0; color: #1976d2;">
              Earn <strong>${pointsToNext} more points</strong> to reach ${
                Object.keys(nextTierPoints)[
                  Object.values(nextTierPoints).indexOf(nextTierPoints[tier])
                ]
              } status!
            </p>
          </div>
        `
            : `
          <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #7b1fa2;">👑 Platinum Member</h4>
            <p style="margin: 0; color: #7b1fa2;">
              You've reached our highest tier! Enjoy all the exclusive benefits.
            </p>
          </div>
        `
        }

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://Lumina.com/account/loyalty"
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    display: inline-block;">
            View Full Rewards History
          </a>
        </div>

        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4 style="margin: 0 0 10px 0; color: #333;">💡 Ways to Earn More Points:</h4>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li>Make purchases (${
              tier === "Platinum"
                ? "2"
                : tier === "Gold"
                ? "1.5"
                : tier === "Silver"
                ? "1.25"
                : "1"
            } points per $1)</li>
            <li>Write product reviews (50 points each)</li>
            <li>Refer friends (100 points per successful referral)</li>
            <li>Follow us on social media (25 points)</li>
          </ul>
        </div>

        <p style="color: #666; font-size: 14px;">
          Thank you for being a valued Lumina loyalty member!
        </p>
      `,
      type: "general",
    };
  },
};

module.exports = {
  sendEmail,
  emailTemplates,
  getEmailTemplate,
  logEmail,
  initializeSendGrid,
};
