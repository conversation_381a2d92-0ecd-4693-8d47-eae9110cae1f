import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { useTheme } from "../../context/ThemeContext";
import { useAuth } from "../../context/AuthContext";
import { colors, spacing, typography } from "../../styles";
import {
  FaBox,
  FaPen,
  FaEnvelope,
  FaStar,
  FaBars,
  FaTimes,
  FaShoppingBag,
  FaPaperPlane,
} from "react-icons/fa";
import ProductManager from "../../components/admin/ProductManager";
import BlogManager from "../../components/admin/BlogManager";
import MessageManager from "../../components/admin/MessageManager";
import ReviewManager from "../../components/admin/ReviewManager";
import OrderManager from "../../components/admin/OrderManager";
import EmailManager from "../../components/admin/EmailManager";

const DashboardContainer = styled.div`
  display: flex;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.lightest};
  min-height: calc(100vh - 120px);
`;

const Sidebar = styled.div`
  width: ${(props) => (props.isCollapsed ? "60px" : "240px")};
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.black : colors.neutral.white};
  border-right: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  transition: width 0.3s ease;
  overflow: hidden;
  position: sticky;
  top: 0;
  height: 100vh;
`;

const SidebarHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: ${(props) =>
    props.isCollapsed ? "center" : "space-between"};
  padding: ${spacing.md};
  border-bottom: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
`;

const ToggleButton = styled.button`
  background: none;
  border: none;
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  cursor: pointer;
  font-size: 1.2rem;
  padding: ${spacing.xs};
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SidebarNav = styled.nav`
  display: flex;
  flex-direction: column;
  padding: ${spacing.sm} 0;
`;

const NavItem = styled.button`
  display: flex;
  align-items: center;
  padding: ${spacing.sm}
    ${(props) => (props.isCollapsed ? spacing.xs : spacing.md)};
  color: ${(props) =>
    props.active
      ? props.darkMode
        ? colors.primary.light
        : colors.primary.main
      : props.darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  background-color: ${(props) =>
    props.active
      ? props.darkMode
        ? "rgba(248, 179, 197, 0.1)"
        : "rgba(248, 179, 197, 0.1)"
      : "transparent"};
  border: none;
  cursor: pointer;
  text-align: left;
  width: 100%;
  transition: background-color 0.2s;
  border-left: 3px solid
    ${(props) => (props.active ? colors.primary.main : "transparent")};

  &:hover {
    background-color: ${(props) =>
      props.darkMode ? "rgba(248, 179, 197, 0.1)" : "rgba(248, 179, 197, 0.1)"};
  }
`;

const IconWrapper = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  width: ${(props) => (props.isCollapsed ? "100%" : "24px")};
  margin-right: ${(props) => (props.isCollapsed ? "0" : spacing.sm)};
`;

const NavText = styled.span`
  display: ${(props) => (props.isCollapsed ? "none" : "block")};
  white-space: nowrap;
  font-weight: ${(props) =>
    props.active
      ? typography.fontWeight.medium
      : typography.fontWeight.regular};
`;

const ContentArea = styled.div`
  flex: 1;
  padding: ${spacing.lg};
  overflow-y: auto;
`;

const DashboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.lg};

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${spacing.sm};
  }

  h1 {
    margin-bottom: 0;
    color: ${(props) =>
      props.darkMode ? colors.neutral.white : colors.neutral.dark};

    @media (max-width: 768px) {
      font-size: 1.5rem;
    }
  }
`;

const Dashboard = () => {
  const { darkMode } = useTheme();
  const {
    currentUser,
    isAdmin,
    refreshUserClaims,
    userClaims,
    savePreviousLocation,
  } = useAuth();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("products");
  const [isCollapsed, setIsCollapsed] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const checkAdminStatus = async () => {
      console.log("Checking admin status, current user:", currentUser?.email);

      if (!currentUser) {
        console.log("No current user, redirecting to login");
        // Save the current location before redirecting
        savePreviousLocation("/admin");
        navigate("/login");
        return;
      }

      try {
        // Use the centralized isAdmin from AuthContext
        if (!isAdmin) {
          console.log("Not an admin, redirecting");
          navigate("/");
          return;
        }
      } catch (error) {
        console.error("Error checking admin status:", error);
        navigate("/");
        return;
      }

      setLoading(false);
    };

    checkAdminStatus();
  }, [currentUser, navigate, isAdmin]);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  if (loading) {
    return <div>Loading admin dashboard...</div>;
  }

  return (
    <DashboardContainer darkMode={darkMode}>
      <Sidebar darkMode={darkMode} isCollapsed={isCollapsed}>
        <SidebarHeader darkMode={darkMode} isCollapsed={isCollapsed}>
          {!isCollapsed && <span>Admin Panel</span>}
          <ToggleButton onClick={toggleSidebar} darkMode={darkMode}>
            {isCollapsed ? <FaBars /> : <FaTimes />}
          </ToggleButton>
        </SidebarHeader>

        <SidebarNav>
          <NavItem
            darkMode={darkMode}
            active={activeTab === "products"}
            onClick={() => setActiveTab("products")}
            isCollapsed={isCollapsed}
          >
            <IconWrapper isCollapsed={isCollapsed}>
              <FaBox />
            </IconWrapper>
            <NavText
              isCollapsed={isCollapsed}
              active={activeTab === "products"}
            >
              Products
            </NavText>
          </NavItem>

          <NavItem
            darkMode={darkMode}
            active={activeTab === "blog"}
            onClick={() => setActiveTab("blog")}
            isCollapsed={isCollapsed}
          >
            <IconWrapper isCollapsed={isCollapsed}>
              <FaPen />
            </IconWrapper>
            <NavText isCollapsed={isCollapsed} active={activeTab === "blog"}>
              Blog
            </NavText>
          </NavItem>

          <NavItem
            darkMode={darkMode}
            active={activeTab === "messages"}
            onClick={() => setActiveTab("messages")}
            isCollapsed={isCollapsed}
          >
            <IconWrapper isCollapsed={isCollapsed}>
              <FaEnvelope />
            </IconWrapper>
            <NavText
              isCollapsed={isCollapsed}
              active={activeTab === "messages"}
            >
              Messages
            </NavText>
          </NavItem>

          <NavItem
            darkMode={darkMode}
            active={activeTab === "reviews"}
            onClick={() => setActiveTab("reviews")}
            isCollapsed={isCollapsed}
          >
            <IconWrapper isCollapsed={isCollapsed}>
              <FaStar />
            </IconWrapper>
            <NavText isCollapsed={isCollapsed} active={activeTab === "reviews"}>
              Reviews
            </NavText>
          </NavItem>

          <NavItem
            darkMode={darkMode}
            active={activeTab === "orders"}
            onClick={() => setActiveTab("orders")}
            isCollapsed={isCollapsed}
          >
            <IconWrapper isCollapsed={isCollapsed}>
              <FaShoppingBag />
            </IconWrapper>
            <NavText isCollapsed={isCollapsed} active={activeTab === "orders"}>
              Orders
            </NavText>
          </NavItem>

          <NavItem
            darkMode={darkMode}
            active={activeTab === "emails"}
            onClick={() => setActiveTab("emails")}
            isCollapsed={isCollapsed}
          >
            <IconWrapper isCollapsed={isCollapsed}>
              <FaPaperPlane />
            </IconWrapper>
            <NavText isCollapsed={isCollapsed} active={activeTab === "emails"}>
              Emails
            </NavText>
          </NavItem>
        </SidebarNav>
      </Sidebar>

      <ContentArea>
        <DashboardHeader darkMode={darkMode}>
          <h1>Admin Dashboard</h1>
          <p>Welcome, {currentUser?.displayName || currentUser?.email}</p>
        </DashboardHeader>

        {activeTab === "products" && <ProductManager darkMode={darkMode} />}
        {activeTab === "blog" && <BlogManager darkMode={darkMode} />}
        {activeTab === "messages" && <MessageManager darkMode={darkMode} />}
        {activeTab === "reviews" && <ReviewManager darkMode={darkMode} />}
        {activeTab === "orders" && <OrderManager darkMode={darkMode} />}
        {activeTab === "emails" && <EmailManager darkMode={darkMode} />}
      </ContentArea>
    </DashboardContainer>
  );
};

export default Dashboard;
