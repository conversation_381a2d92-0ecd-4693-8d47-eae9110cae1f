import { useState, useEffect } from "react";
import { collection, getDocs, query, orderBy, limit, where } from "firebase/firestore";
import { db } from "../../firebase/config";
import styled from "styled-components";
import { colors, spacing } from "../../styles";

const Container = styled.div`
  padding: ${spacing.lg};
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  margin-bottom: ${spacing.lg};
`;

const Title = styled.h3`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darker};
  margin-bottom: ${spacing.md};
`;

const EmailList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.sm};
`;

const EmailItem = styled.div`
  padding: ${spacing.sm};
  background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.lighter};
  border-radius: 4px;
  border-left: 4px solid ${props => {
    switch(props.status) {
      case 'sent': return colors.success.main;
      case 'failed': return colors.error.main;
      case 'pending': return colors.warning.main;
      default: return colors.neutral.gray;
    }
  }};
`;

const EmailHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.xs};
`;

const EmailSubject = styled.h4`
  margin: 0;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darker};
  font-size: 14px;
`;

const EmailStatus = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  background: ${props => {
    switch(props.status) {
      case 'sent': return colors.success.main;
      case 'failed': return colors.error.main;
      case 'pending': return colors.warning.main;
      default: return colors.neutral.gray;
    }
  }};
`;

const EmailDetails = styled.div`
  font-size: 12px;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const LoadingText = styled.p`
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.gray};
  text-align: center;
  padding: ${spacing.lg};
`;

const EmailTracker = ({ darkMode, orderRelated = false }) => {
  const [emails, setEmails] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRecentEmails();
  }, [orderRelated]);

  const fetchRecentEmails = async () => {
    try {
      setLoading(true);
      const emailsRef = collection(db, "email_logs");
      
      let emailQuery = query(
        emailsRef,
        orderBy("timestamp", "desc"),
        limit(10)
      );

      if (orderRelated) {
        emailQuery = query(
          emailsRef,
          where("type", "in", ["order", "general"]),
          orderBy("timestamp", "desc"),
          limit(10)
        );
      }

      const snapshot = await getDocs(emailQuery);
      const emailsList = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      }));

      setEmails(emailsList);
    } catch (error) {
      console.error("Error fetching emails:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date) => {
    if (!date) return "Unknown";
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (loading) {
    return (
      <Container darkMode={darkMode}>
        <LoadingText darkMode={darkMode}>Loading recent emails...</LoadingText>
      </Container>
    );
  }

  return (
    <Container darkMode={darkMode}>
      <Title darkMode={darkMode}>
        Recent {orderRelated ? "Order-Related " : ""}Emails ({emails.length})
      </Title>
      
      {emails.length === 0 ? (
        <LoadingText darkMode={darkMode}>
          No {orderRelated ? "order-related " : ""}emails found
        </LoadingText>
      ) : (
        <EmailList>
          {emails.map(email => (
            <EmailItem key={email.id} darkMode={darkMode} status={email.status}>
              <EmailHeader>
                <EmailSubject darkMode={darkMode}>
                  {email.subject || "No Subject"}
                </EmailSubject>
                <EmailStatus status={email.status}>
                  {email.status || "unknown"}
                </EmailStatus>
              </EmailHeader>
              <EmailDetails darkMode={darkMode}>
                To: {email.to} | {formatDate(email.timestamp)}
                {email.orderReference && ` | Order: ${email.orderReference}`}
              </EmailDetails>
            </EmailItem>
          ))}
        </EmailList>
      )}
    </Container>
  );
};

export default EmailTracker;
