import { useState, useEffect, useRef } from "react";
import { useNavigate, Link } from "react-router-dom";
import styled from "styled-components";
import { auth, db, functions } from "../firebase/config";
import { useTheme } from "../context/ThemeContext";
import { useWishlist } from "../context/WishlistContext";
import { useCart } from "../context/CartContext";
import { colors, spacing, typography } from "../styles";
import { httpsCallable } from "firebase/functions";
import { formatCurrency, formatDate } from "../utils/formatters";
import {
  FaUser,
  FaUpload,
  FaShoppingBag,
  FaHeart,
  FaAddressCard,
  FaCreditCard,
  FaBell,
  FaChevronLeft,
  FaChevronRight,
  FaEdit,
  FaSave,
  FaTimes,
  FaBirthdayCake,
  FaPhone,
  FaPlus,
  FaTrash,
  FaStar,
  FaShoppingCart,
  FaEye,
  FaRedo,
  <PERSON>a<PERSON>x<PERSON><PERSON>riangle,
} from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";
import { uploadImage } from "../utils/fileUpload";
import { updateProfile, deleteUser } from "firebase/auth";
import {
  doc,
  setDoc,
  getDoc,
  updateDoc,
  collection,
  getDocs,
  addDoc,
  writeBatch,
  deleteDoc,
  query,
  where,
  orderBy,
} from "firebase/firestore";
// import { format } from "date-fns";

// Add new styled components for the edit mode
const EditableField = styled.div`
  margin-bottom: ${spacing.md};
  width: 100%;

  @media (max-width: 768px) {
    margin-bottom: ${spacing.sm};
  }
`;

const FieldLabel = styled.label`
  display: block;
  margin-bottom: ${spacing.xs};
  font-weight: ${typography.fontWeight.medium};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const FieldInput = styled.input`
  width: 100%;
  padding: ${spacing.sm};
  border-radius: 4px;
  border: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.dark : colors.neutral.light)};
  background-color: ${(props) =>
    props.$darkMode ? colors.neutral.black : colors.neutral.white};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.dark};
  font-family: ${typography.fontFamily.primary};
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 2px ${colors.primary.light}40;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${spacing.sm};

  @media (max-width: 480px) {
    gap: ${spacing.xs};
    flex-wrap: wrap;
  }
`;

const IconButton = styled(motion.button)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: ${(props) =>
    props.variant === "primary"
      ? props.$darkMode
        ? colors.primary.dark
        : colors.primary.main
      : props.$darkMode
      ? colors.neutral.dark
      : colors.neutral.lightest};
  color: ${(props) =>
    props.variant === "primary"
      ? colors.neutral.white
      : props.$darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: none;
  border-radius: 4px;
  padding: ${(props) =>
    props.small ? spacing.xs : `${spacing.xs} ${spacing.sm}`};
  cursor: pointer;
  font-family: ${typography.fontFamily.primary};
  font-size: ${(props) =>
    props.small ? typography.fontSize.sm : typography.fontSize.md};
  transition: all 0.2s;

  &:hover {
    background: ${(props) =>
      props.variant === "primary"
        ? props.$darkMode
          ? colors.primary.main
          : colors.primary.light
        : props.$darkMode
        ? colors.neutral.darker
        : colors.neutral.lighter};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    margin-right: ${(props) => (props.iconOnly ? "0" : spacing.xs)};
  }

  @media (max-width: 768px) {
    padding: ${(props) =>
      props.small ? spacing.xxs || "4px" : `${spacing.xs} ${spacing.sm}`};
  }

  @media (max-width: 480px) {
    padding: ${(props) => (props.small ? spacing.xxs || "4px" : spacing.xs)};
    font-size: ${(props) =>
      props.small ? typography.fontSize.xs : typography.fontSize.sm};

    svg {
      margin-right: ${(props) => (props.iconOnly ? "0" : "2px")};
    }
  }
`;

const ProfileField = styled.div`
  margin-bottom: ${spacing.md};
  word-break: break-word; // Allow long words to break

  strong {
    display: inline-block;
    min-width: 80px;
    margin-right: ${spacing.sm};
  }

  @media (max-width: 480px) {
    strong {
      display: block;
      margin-bottom: ${spacing.xs};
    }
  }
`;

const AccountContainer = styled.div`
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 200px);

  @media (max-width: 768px) {
    flex-direction: row; // Keep as row instead of column
    padding: 0 ${spacing.sm};
    overflow-x: hidden; // Prevent horizontal scrolling
  }
`;

const Sidebar = styled.div`
  width: ${(props) => (props.$isCollapsed ? "70px" : "250px")};
  min-width: ${(props) =>
    props.$isCollapsed
      ? "70px"
      : "250px"}; // Add min-width to prevent shrinking
  background: ${(props) =>
    props.$darkMode ? colors.neutral.black : colors.neutral.lightest};
  border-radius: 12px;
  padding: ${spacing.md};
  margin-right: ${spacing.lg};
  transition: all 0.3s ease;
  border: ${(props) =>
    props.$darkMode ? `1px solid ${colors.neutral.dark}` : "none"};
  box-shadow: ${(props) =>
    props.$darkMode ? "none" : "0 4px 12px rgba(0,0,0,0.1)"};
  position: sticky;
  top: 20px;
  height: fit-content;

  @media (max-width: 768px) {
    width: ${(props) => (props.$isCollapsed ? "70px" : "200px")};
    min-width: ${(props) => (props.$isCollapsed ? "70px" : "200px")};
    margin-right: ${spacing.md};
    padding: ${(props) => (props.$isCollapsed ? spacing.sm : spacing.md)};
  }

  @media (max-width: 480px) {
    width: ${(props) => (props.$isCollapsed ? "50px" : "160px")};
    min-width: ${(props) => (props.$isCollapsed ? "50px" : "160px")};
    padding: ${(props) => (props.$isCollapsed ? spacing.xs : spacing.sm)};
  }
`;

const SidebarHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: ${(props) =>
    props.$isCollapsed ? "center" : "space-between"};
  margin-bottom: ${spacing.lg};
  padding-bottom: ${spacing.md};
  border-bottom: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.dark : colors.neutral.light)};
`;

const SidebarTitle = styled.h2`
  margin: 0;
  font-size: ${(props) => (props.$isCollapsed ? "0" : typography.fontSize.lg)};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.darkest};
  overflow: hidden;
  white-space: nowrap;
`;

const ToggleButton = styled.button`
  background: none;
  border: none;
  color: ${(props) =>
    props.$darkMode ? colors.primary.light : colors.primary.main};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${spacing.xs};
  border-radius: 50%;
  transition: background 0.2s;

  &:hover {
    background: ${(props) =>
      props.$darkMode
        ? "rgba(248, 179, 197, 0.1)"
        : "rgba(248, 179, 197, 0.1)"};
  }

  @media (max-width: 480px) {
    padding: ${spacing.xxs || "4px"};
  }
`;

// Add a mobile-specific toggle button that appears at the top on very small screens
const MobileToggleButton = styled.button`
  display: none;
  background: ${(props) =>
    props.$darkMode ? colors.primary.dark : colors.primary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: ${spacing.xs} ${spacing.sm};
  margin-bottom: ${spacing.sm};
  cursor: pointer;

  @media (max-width: 480px) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
`;

const NavItems = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.sm};
`;

const NavItem = ({ $isCollapsed, $darkMode, active, children, ...rest }) => (
  <StyledNavItem
    $isCollapsed={$isCollapsed}
    $darkMode={$darkMode}
    active={active}
    {...rest}
  >
    {children}
  </StyledNavItem>
);

const StyledNavItem = styled(motion.button)`
  display: flex;
  align-items: center;
  padding: ${spacing.sm}
    ${(props) => (props.$isCollapsed ? spacing.xs : spacing.md)};
  border-radius: 8px;
  background: ${(props) =>
    props.active
      ? props.$darkMode
        ? "rgba(248, 179, 197, 0.15)"
        : "rgba(248, 179, 197, 0.15)"
      : "transparent"};
  color: ${(props) =>
    props.active
      ? props.$darkMode
        ? colors.primary.light
        : colors.primary.main
      : props.$darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
  transition: all 0.2s;

  &:hover {
    background: ${(props) =>
      props.$darkMode
        ? "rgba(248, 179, 197, 0.1)"
        : "rgba(248, 179, 197, 0.1)"};
  }
`;

const IconWrapper = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  width: ${(props) => (props.$isCollapsed ? "100%" : "24px")};
  margin-right: ${(props) => (props.$isCollapsed ? "0" : spacing.md)};
  font-size: 1.2rem;
`;

const NavText = styled.span`
  white-space: nowrap;
  overflow: hidden;
  max-width: ${(props) => (props.$isCollapsed ? "0" : "200px")};
  opacity: ${(props) => (props.$isCollapsed ? 0 : 1)};
  transition: max-width 0.3s ease, opacity 0.2s ease;
  transition-delay: ${(props) => (props.$isCollapsed ? "0s" : "0.1s")};
`;

const ContentArea = styled.div`
  flex: 1;
  padding: ${spacing.md};
  overflow-x: hidden; // Prevent content from bleeding out

  @media (max-width: 768px) {
    padding: ${spacing.sm};
  }
`;

const AccountSection = styled(motion.div)`
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: ${(props) =>
    props.$darkMode ? colors.neutral.black : colors.neutral.lightest};
  border-radius: 12px;
  border: ${(props) =>
    props.$darkMode ? `1px solid ${colors.neutral.dark}` : "none"};
  box-shadow: ${(props) =>
    props.$darkMode ? "none" : "0 2px 8px rgba(0,0,0,0.1)"};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    padding: ${spacing.md};
  }

  @media (max-width: 480px) {
    padding: ${spacing.sm};
  }
`;

const SectionTitle = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: wrap; // Allow items to wrap
  gap: ${spacing.sm}; // Add gap for spacing when wrapped
  margin-bottom: ${spacing.md};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.darker};
  font-size: ${typography.fontSize.lg};
  font-weight: ${typography.fontWeight.bold};

  svg {
    margin-right: ${spacing.sm};
  }

  @media (max-width: 768px) {
    margin-bottom: ${spacing.sm};
  }

  @media (max-width: 480px) {
    font-size: ${typography.fontSize.md};
    margin-bottom: ${spacing.xs};

    // Push button to next line on very small screens
    justify-content: space-between;

    // Add some spacing when the button wraps to next line
    & > button {
      margin-top: ${spacing.xs};
    }
  }
`;

const ProfileImageWrapper = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;

  @media (max-width: 600px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${spacing.md};
  }
`;

const ProfileImage = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: ${colors.primary.main};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-right: ${spacing.md};

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media (max-width: 768px) {
    margin-right: 0;
  }
`;

const UploadButton = styled.label`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  background: ${(props) =>
    props.$darkMode ? colors.primary.dark : colors.primary.main};
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: ${typography.fontSize.sm};

  &:hover {
    background: ${(props) =>
      props.$darkMode ? colors.primary.darkest : colors.primary.dark};
  }

  input {
    display: none;
  }
`;

const Button = styled.button`
  background: ${(props) =>
    props.$darkMode ? colors.primary.dark : colors.primary.main};
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: ${(props) =>
      props.$darkMode ? colors.primary.darkest : colors.primary.dark};
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${spacing.xl} 0;
  color: ${(props) =>
    props.$darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const OrdersTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: ${spacing.lg};
`;

const OrdersTh = styled.th`
  text-align: left;
  padding: ${spacing.sm};
  border-bottom: 2px solid
    ${(props) => (props.$darkMode ? colors.neutral.dark : colors.neutral.light)};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.dark};
  font-weight: ${typography.fontWeight.medium};
`;

const OrdersTd = styled.td`
  padding: ${spacing.sm};
  border-bottom: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.dark : colors.neutral.light)};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const OrderStatusBadge = styled.span`
  display: inline-block;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 12px;
  font-size: ${typography.fontSize.xs};
  font-weight: ${typography.fontWeight.medium};
  text-transform: uppercase;
  background-color: ${(props) => {
    switch (props.status) {
      case "processing":
        return colors.info.light;
      case "shipped":
        return colors.warning.light;
      case "delivered":
        return colors.success.light;
      case "cancelled":
        return colors.error.light;
      case "refunded":
        return colors.neutral.light;
      default:
        return colors.neutral.light;
    }
  }};
  color: ${(props) => {
    switch (props.status) {
      case "processing":
        return colors.info.dark;
      case "shipped":
        return colors.warning.dark;
      case "delivered":
        return colors.success.dark;
      case "cancelled":
        return colors.error.dark;
      case "refunded":
        return colors.neutral.dark;
      default:
        return colors.neutral.dark;
    }
  }};
`;

const OrderActionButton = styled(IconButton)`
  padding: ${spacing.xs};
  margin-right: ${spacing.xs};
`;

const OrderPagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: ${spacing.md};
`;

const OrderPageButton = styled.button`
  padding: ${spacing.xs} ${spacing.sm};
  background-color: ${(props) =>
    props.active
      ? colors.primary.main
      : props.$darkMode
      ? colors.neutral.dark
      : colors.neutral.white};
  color: ${(props) =>
    props.active
      ? colors.neutral.white
      : props.$darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: 1px solid
    ${(props) =>
      props.active
        ? colors.primary.main
        : props.$darkMode
        ? colors.neutral.dark
        : colors.neutral.light};
  border-radius: 4px;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};
  margin: 0 ${spacing.xs};

  &:hover:not(:disabled) {
    background-color: ${(props) =>
      props.active
        ? colors.primary.dark
        : props.$darkMode
        ? colors.neutral.darker
        : colors.neutral.lighter};
  }
`;

const AddressGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${spacing.lg}; // Increased from spacing.md

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: ${spacing.md}; // Increased from spacing.sm
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: ${spacing.sm}; // Increased from spacing.xs
  }
`;

const AddressCard = styled.div`
  padding: ${spacing.md};
  border-radius: 8px;
  border: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.dark : colors.neutral.light)};
  position: relative;

  @media (max-width: 768px) {
    padding: ${spacing.sm};
  }

  @media (max-width: 480px) {
    padding: ${spacing.xs};

    /* Make text smaller on mobile */
    font-size: ${typography.fontSize.sm};

    /* Add more spacing for the button group */
    ${ButtonGroup} {
      margin-top: ${spacing.xs};
    }
  }
`;

const WishlistGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${spacing.md};
`;

const WishlistItem = styled.div`
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid
    ${(props) => (props.$darkMode ? colors.neutral.dark : colors.neutral.light)};
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  img {
    width: 100%;
    height: 150px;
    object-fit: cover;
  }

  .details {
    padding: ${spacing.sm};
  }
`;

const WishlistItemName = styled.h3`
  font-size: ${typography.fontSize.md};
  margin-bottom: ${spacing.xs};
  color: ${(props) =>
    props.$darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const WishlistItemPrice = styled.p`
  font-weight: ${typography.fontWeight.medium};
  color: ${(props) =>
    props.$darkMode ? colors.primary.light : colors.primary.dark};
  margin-bottom: ${spacing.xs};
`;

const WishlistItemActions = styled.div`
  display: flex;
  gap: ${spacing.xs};
  margin-top: ${spacing.sm};
`;

const WishlistItemButton = styled.button`
  background: ${(props) =>
    props.primary
      ? props.$darkMode
        ? colors.primary.dark
        : colors.primary.main
      : "transparent"};
  color: ${(props) =>
    props.primary
      ? "white"
      : props.$darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: ${(props) =>
    props.primary
      ? "none"
      : `1px solid ${
          props.$darkMode ? colors.neutral.light : colors.neutral.dark
        }`};
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 4px;
  font-size: ${typography.fontSize.sm};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;

  &:hover {
    background: ${(props) =>
      props.primary
        ? props.$darkMode
          ? colors.primary.main
          : colors.primary.light
        : props.$darkMode
        ? colors.neutral.dark
        : colors.neutral.light};
  }

  svg {
    margin-right: ${spacing.xs};
  }
`;

const Badge = styled.span`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: ${(props) =>
    props.$darkMode ? colors.primary.main : colors.primary.light};
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: ${typography.fontSize.xs};
  font-weight: ${typography.fontWeight.medium};
`;

const DefaultBadge = styled.span`
  position: absolute;
  top: ${spacing.xs};
  right: ${spacing.xs};
  background: ${colors.success.main};
  color: white;
  font-size: ${typography.fontSize.xs};
  padding: 2px 6px;
  border-radius: 4px;
`;

const ModalWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${(props) =>
    props.$darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  padding: ${spacing.lg};
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.md};

  h3 {
    margin: 0;
    color: ${(props) =>
      props.$darkMode ? colors.neutral.white : colors.neutral.darker};
  }
`;

const FormRow = styled.div`
  display: flex;
  gap: ${spacing.md};

  > * {
    flex: 1;
  }

  @media (max-width: 600px) {
    flex-direction: column;
    gap: ${spacing.sm};
  }
`;

// If you have any tables or data grids, make them scrollable on mobile
const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

  table {
    min-width: 500px;
  }
`;

// For section titles with action buttons
const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.md};

  @media (max-width: 600px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${spacing.sm};

    button {
      width: 100%;
      margin-top: ${spacing.xs};
    }
  }
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  cursor: pointer;
  color: ${(props) =>
    props.$darkMode ? colors.neutral.light : colors.neutral.dark};

  input {
    margin: 0;
  }
`;

const Modal = ({ isOpen, onClose, title, children, $darkMode }) => {
  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        style={{
          backgroundColor: $darkMode
            ? colors.neutral.darker
            : colors.neutral.white,
          borderRadius: "8px",
          padding: spacing.lg,
          width: "90%",
          maxWidth: "500px",
          maxHeight: "90vh",
          overflowY: "auto",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: spacing.md,
          }}
        >
          <h3
            style={{
              margin: 0,
              color: $darkMode ? colors.neutral.white : colors.neutral.darker,
            }}
          >
            {title}
          </h3>
          <IconButton
            $darkMode={$darkMode}
            onClick={onClose}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            style={{ padding: spacing.xs }}
          >
            <FaTimes />
          </IconButton>
        </div>
        {children}
      </motion.div>
    </motion.div>
  );
};

// Import the DeleteConfirmationModal
import DeleteConfirmationModal from "../components/ui/DeleteConfirmationModal";

const Account = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [profileImage, setProfileImage] = useState(null);
  const [activeSection, setActiveSection] = useState("profile");
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [userProfile, setUserProfile] = useState({
    displayName: "",
    phoneNumber: "",
    dateOfBirth: "",
    gender: "",
    bio: "",
  });
  const [originalProfile, setOriginalProfile] = useState({});
  const navigate = useNavigate();
  const { darkMode } = useTheme();
  const {
    wishlistItems,
    removeFromWishlist,
    loading: wishlistLoading,
  } = useWishlist();
  const { addToCart } = useCart();
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [addresses, setAddresses] = useState([]);
  const [newAddress, setNewAddress] = useState({
    name: "",
    street: "",
    city: "",
    state: "",
    zip: "",
    country: "US",
    isDefault: false,
  });
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [editingAddressId, setEditingAddressId] = useState(null);
  const [deleteAccountModal, setDeleteAccountModal] = useState(false);
  const [deleteAttempts, setDeleteAttempts] = useState(0);
  const [deleteButtonPosition, setDeleteButtonPosition] = useState({
    x: 0,
    y: 0,
  });
  const deleteButtonRef = useRef(null);

  // Order state
  const [orders, setOrders] = useState([]);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [ordersError, setOrdersError] = useState(null);
  const [currentOrderPage, setCurrentOrderPage] = useState(1);
  const [ordersPerPage] = useState(5);
  const [totalUserOrders, setTotalUserOrders] = useState(0);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Handle adding a wishlist item to cart
  const handleAddToCart = (item) => {
    addToCart(item);
  };

  const handleDeleteAccountAttempt = () => {
    const messages = [
      "Are you sure?",
      "Are you ABSOLUTELY sure?",
      "🤷‍♀️ Maybe this isn't what you meant to do...",
      "No, please don't leave us! 😢",
      "We love you! 💔 😭",
      "😔 Okay fine, you have a right to leave...",
    ];

    // Increment attempts
    const newAttempts = deleteAttempts + 1;
    setDeleteAttempts(newAttempts);

    // If enough attempts, show confirmation modal
    if (newAttempts >= 6) {
      setDeleteAccountModal(true);
      return;
    }

    // Move the button to a random position
    if (deleteButtonRef.current) {
      const parentRect =
        deleteButtonRef.current.parentElement.getBoundingClientRect();
      const buttonRect = deleteButtonRef.current.getBoundingClientRect();

      // Calculate random position within parent boundaries
      const maxX = parentRect.width - buttonRect.width;
      const maxY = 100; // Limit vertical movement

      const newX = Math.floor(Math.random() * maxX);
      const newY = Math.floor(Math.random() * maxY) - 50; // Allow some negative values for upward movement

      setDeleteButtonPosition({ x: newX, y: newY });
    }
  };

  const handleDeleteAccount = async () => {
    try {
      if (!user) return;

      // Delete user data from Firestore
      // You may want to add more collections if needed
      await deleteDoc(doc(db, "users", user.uid));

      // Delete the user authentication account
      await deleteUser(user);

      // Redirect to home page
      navigate("/");
    } catch (error) {
      console.error("Error deleting account:", error);
    }
  };

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      setUser(currentUser);
      setLoading(false);

      if (!currentUser) {
        navigate("/login");
      } else if (currentUser.photoURL) {
        setProfileImage(currentUser.photoURL);
      }

      if (currentUser) {
        fetchUserProfile(currentUser.uid);
        fetchAddresses(currentUser.uid);
        fetchOrders();
      }
    });

    return () => unsubscribe();
  }, [navigate]);

  // Fetch orders when page changes
  useEffect(() => {
    if (user && activeSection === "orders") {
      fetchOrders();
    }
  }, [user, currentOrderPage, activeSection]);

  // Function to fetch orders
  const fetchOrders = async () => {
    if (!user) {
      console.log("No user found, skipping order fetch");
      return;
    }

    console.log("Fetching orders for user:", user.uid);
    setOrdersLoading(true);
    setOrdersError(null);

    try {
      console.log("Fetching orders for page:", currentOrderPage);

      // Fetch orders directly from Firestore with pagination
      const ordersRef = collection(db, "orders");
      let q = query(
        ordersRef,
        where("userId", "==", user.uid),
        orderBy("createdAt", "desc")
      );

      console.log("Executing Firestore query...");
      const snapshot = await getDocs(q);
      console.log("Query result:", snapshot.size, "documents found");

      // Get all orders first
      const allOrdersList = snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate().toISOString(),
          updatedAt: data.updatedAt?.toDate().toISOString(),
        };
      });

      // Apply client-side pagination
      const startIndex = (currentOrderPage - 1) * ordersPerPage;
      const endIndex = startIndex + ordersPerPage;
      const paginatedOrders = allOrdersList.slice(startIndex, endIndex);

      console.log(
        `Showing ${paginatedOrders.length} orders out of ${allOrdersList.length} total for page ${currentOrderPage}`
      );

      setOrders(paginatedOrders);
      setTotalUserOrders(allOrdersList.length);
    } catch (error) {
      console.error("Error fetching orders:", error);
      setOrdersError("Failed to load your orders. Please try again.");
    } finally {
      setOrdersLoading(false);
    }
  };

  // Function to cancel an order
  const handleCancelOrder = async (orderId) => {
    if (!user) return;

    const confirmCancel = window.confirm(
      "Are you sure you want to cancel this order? This action cannot be undone."
    );

    if (!confirmCancel) return;

    try {
      console.log("Cancelling order:", orderId);

      // Use Firebase Functions callable
      const cancelOrder = httpsCallable(functions, "cancelOrder");
      await cancelOrder({ orderId });

      // Update local state
      setOrders(
        orders.map((order) =>
          order.id === orderId ? { ...order, status: "cancelled" } : order
        )
      );

      // Refresh orders to ensure consistency
      fetchOrders();

      // Show success message
      alert("Order cancelled successfully");
    } catch (error) {
      console.error("Error cancelling order:", error);
      alert(`Failed to cancel order: ${error.message}`);
    }
  };

  // Function to reorder (add all items to cart again)
  const handleReorder = (order) => {
    if (!order.items || order.items.length === 0) return;

    // Add all items to cart
    order.items.forEach((item) => {
      addToCart(item);
    });

    // Navigate to checkout
    navigate("/checkout");
  };

  const fetchUserProfile = async (userId) => {
    try {
      const userDocRef = doc(db, "users", userId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        // Use userData.displayName first, then fall back to auth displayName
        setUserProfile({
          displayName: userData.displayName || user?.displayName || "",
          phoneNumber: userData.phoneNumber || "",
          dateOfBirth: userData.dateOfBirth || "",
          gender: userData.gender || "",
          bio: userData.bio || "",
        });
        setOriginalProfile({
          displayName: userData.displayName || user?.displayName || "",
          phoneNumber: userData.phoneNumber || "",
          dateOfBirth: userData.dateOfBirth || "",
          gender: userData.gender || "",
          bio: userData.bio || "",
        });
      } else {
        // Initialize with auth data if no Firestore profile exists
        setUserProfile({
          displayName: user?.displayName || "",
          phoneNumber: "",
          dateOfBirth: "",
          gender: "",
          bio: "",
        });
        setOriginalProfile({
          displayName: user?.displayName || "",
          phoneNumber: "",
          dateOfBirth: "",
          gender: "",
          bio: "",
        });
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  const handleLogout = async () => {
    try {
      await auth.signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        // Upload to Firebase Storage using our reusable function
        const imageUrl = await uploadImage(file, "profiles");

        // Update the user's profile in Firebase Auth
        if (user) {
          await updateProfile(user, {
            photoURL: imageUrl,
          });

          // Update local state
          setProfileImage(imageUrl);
        }
      } catch (error) {
        console.error("Error uploading profile image:", error);
        // Handle error (show message to user)
      }
    }
  };

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleEditProfile = () => {
    setEditMode(true);
  };

  const handleCancelEdit = () => {
    setUserProfile(originalProfile);
    setEditMode(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUserProfile((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSaveProfile = async () => {
    try {
      setLoading(true);

      // Update displayName in Firebase Auth if user is authenticated
      if (user && user.displayName !== userProfile.displayName) {
        try {
          await updateProfile(user, {
            displayName: userProfile.displayName,
          });
          console.log("Auth profile updated successfully");
        } catch (authError) {
          console.error("Error updating auth profile:", authError);
          // Continue with Firestore update even if auth update fails
        }
      }

      // Save ALL profile data to Firestore regardless of auth update success
      if (user) {
        const userDocRef = doc(db, "users", user.uid);
        await setDoc(
          userDocRef,
          {
            displayName: userProfile.displayName,
            phoneNumber: userProfile.phoneNumber,
            dateOfBirth: userProfile.dateOfBirth,
            gender: userProfile.gender,
            bio: userProfile.bio,
            updatedAt: new Date(),
          },
          { merge: true }
        );

        console.log("Firestore profile updated successfully");

        // Force refresh the user object to get updated displayName
        if (user.reload) {
          await user.reload();
          setUser({ ...auth.currentUser });
        }

        setOriginalProfile({ ...userProfile });
        setEditMode(false);
      }
    } catch (error) {
      console.error("Error saving profile:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Not provided";
    try {
      return new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }).format(new Date(dateString));
    } catch (error) {
      return dateString;
    }
  };

  const fetchAddresses = async (userId) => {
    try {
      const addressesRef = collection(db, "users", userId, "addresses");
      const addressesSnapshot = await getDocs(addressesRef);
      const addressesList = addressesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
      setAddresses(addressesList);
    } catch (error) {
      console.error("Error fetching addresses:", error);
    }
  };

  const handleAddressChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewAddress((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleAddAddress = async () => {
    try {
      if (!user) return;

      if (editingAddressId) {
        // Update existing address
        const addressRef = doc(
          db,
          "users",
          user.uid,
          "addresses",
          editingAddressId
        );
        await updateDoc(addressRef, {
          ...newAddress,
          updatedAt: new Date(),
        });

        // Update state
        setAddresses((prev) =>
          prev.map((addr) =>
            addr.id === editingAddressId
              ? { id: editingAddressId, ...newAddress }
              : addr
          )
        );
      } else {
        // Add new address
        const addressesRef = collection(db, "users", user.uid, "addresses");
        const docRef = await addDoc(addressesRef, {
          ...newAddress,
          createdAt: new Date(),
        });

        // Add the new address to the state with its ID
        setAddresses((prev) => [...prev, { id: docRef.id, ...newAddress }]);
      }

      // If this is the default address, update other addresses
      if (newAddress.isDefault) {
        const batch = writeBatch(db);
        addresses.forEach((address) => {
          if (address.isDefault && address.id !== editingAddressId) {
            const addressRef = doc(
              db,
              "users",
              user.uid,
              "addresses",
              address.id
            );
            batch.update(addressRef, { isDefault: false });
          }
        });
        await batch.commit();

        // Update state for other addresses
        if (!editingAddressId) {
          setAddresses((prev) =>
            prev.map((addr) => ({
              ...addr,
              isDefault: addr.id === docRef.id ? true : false,
            }))
          );
        } else {
          setAddresses((prev) =>
            prev.map((addr) => ({
              ...addr,
              isDefault: addr.id === editingAddressId,
            }))
          );
        }
      }

      // Reset the form
      setNewAddress({
        name: "",
        street: "",
        city: "",
        state: "",
        zip: "",
        country: "US",
        isDefault: false,
      });

      // Reset editing state
      setEditingAddressId(null);

      // Close the form
      setShowAddressForm(false);
    } catch (error) {
      console.error("Error adding/updating address:", error);
    }
  };

  const handleEditAddress = (addressId) => {
    const addressToEdit = addresses.find((addr) => addr.id === addressId);
    if (addressToEdit) {
      setNewAddress({ ...addressToEdit });
      setEditingAddressId(addressId);
      setShowAddressForm(true);
    }
  };

  const handleDeleteAddress = async (addressId) => {
    try {
      if (!user) return;

      // Check if this is the default address
      const addressToDelete = addresses.find((addr) => addr.id === addressId);

      // Delete from Firestore
      await deleteDoc(doc(db, "users", user.uid, "addresses", addressId));

      // Update state
      setAddresses((prev) => prev.filter((addr) => addr.id !== addressId));

      // If we deleted the default address and have other addresses, make another one default
      if (addressToDelete.isDefault && addresses.length > 1) {
        const newDefaultAddress = addresses.find(
          (addr) => addr.id !== addressId
        );
        if (newDefaultAddress) {
          await updateDoc(
            doc(db, "users", user.uid, "addresses", newDefaultAddress.id),
            {
              isDefault: true,
            }
          );

          // Update state
          setAddresses((prev) =>
            prev.map((addr) =>
              addr.id === newDefaultAddress.id
                ? { ...addr, isDefault: true }
                : addr
            )
          );
        }
      }
    } catch (error) {
      console.error("Error deleting address:", error);
    }
  };

  const handleSetDefaultAddress = async (addressId) => {
    try {
      if (!user) return;

      const batch = writeBatch(db);

      // Set all addresses to non-default
      addresses.forEach((address) => {
        if (address.isDefault) {
          const addressRef = doc(
            db,
            "users",
            user.uid,
            "addresses",
            address.id
          );
          batch.update(addressRef, { isDefault: false });
        }
      });

      // Set the selected address as default
      const newDefaultRef = doc(db, "users", user.uid, "addresses", addressId);
      batch.update(newDefaultRef, { isDefault: true });

      await batch.commit();

      // Update state
      setAddresses((prev) =>
        prev.map((addr) => ({
          ...addr,
          isDefault: addr.id === addressId,
        }))
      );
    } catch (error) {
      console.error("Error setting default address:", error);
    }
  };

  if (loading) {
    return (
      <AccountContainer>
        <p>Loading account information...</p>
      </AccountContainer>
    );
  }

  return (
    <AccountContainer>
      <Sidebar $darkMode={darkMode} $isCollapsed={isCollapsed}>
        <SidebarHeader $darkMode={darkMode} $isCollapsed={isCollapsed}>
          <SidebarTitle $darkMode={darkMode} $isCollapsed={isCollapsed}>
            My Account
          </SidebarTitle>
          <ToggleButton onClick={toggleSidebar} $darkMode={darkMode}>
            {isCollapsed ? <FaChevronRight /> : <FaChevronLeft />}
          </ToggleButton>
        </SidebarHeader>

        <NavItems>
          <NavItem
            $darkMode={darkMode}
            active={activeSection === "profile"}
            onClick={() => setActiveSection("profile")}
            $isCollapsed={isCollapsed}
            whileHover={{ x: 5 }}
            whileTap={{ scale: 0.98 }}
          >
            <IconWrapper $isCollapsed={isCollapsed}>
              <FaUser />
            </IconWrapper>
            <NavText $isCollapsed={isCollapsed}>Profile</NavText>
          </NavItem>

          <NavItem
            $darkMode={darkMode}
            active={activeSection === "orders"}
            onClick={() => setActiveSection("orders")}
            $isCollapsed={isCollapsed}
            whileHover={{ x: 5 }}
            whileTap={{ scale: 0.98 }}
          >
            <IconWrapper $isCollapsed={isCollapsed}>
              <FaShoppingBag />
            </IconWrapper>
            <NavText $isCollapsed={isCollapsed}>Orders</NavText>
          </NavItem>

          <NavItem
            $darkMode={darkMode}
            active={activeSection === "wishlist"}
            onClick={() => setActiveSection("wishlist")}
            $isCollapsed={isCollapsed}
            whileHover={{ x: 5 }}
            whileTap={{ scale: 0.98 }}
          >
            <IconWrapper $isCollapsed={isCollapsed}>
              <FaHeart />
            </IconWrapper>
            <NavText $isCollapsed={isCollapsed}>Wishlist</NavText>
          </NavItem>

          <NavItem
            $darkMode={darkMode}
            active={activeSection === "addresses"}
            onClick={() => setActiveSection("addresses")}
            $isCollapsed={isCollapsed}
            whileHover={{ x: 5 }}
            whileTap={{ scale: 0.98 }}
          >
            <IconWrapper $isCollapsed={isCollapsed}>
              <FaAddressCard />
            </IconWrapper>
            <NavText $isCollapsed={isCollapsed}>Addresses</NavText>
          </NavItem>

          <NavItem
            $darkMode={darkMode}
            active={activeSection === "payment"}
            onClick={() => setActiveSection("payment")}
            $isCollapsed={isCollapsed}
            whileHover={{ x: 5 }}
            whileTap={{ scale: 0.98 }}
          >
            <IconWrapper $isCollapsed={isCollapsed}>
              <FaCreditCard />
            </IconWrapper>
            <NavText $isCollapsed={isCollapsed}>Payment Methods</NavText>
          </NavItem>

          <NavItem
            $darkMode={darkMode}
            active={activeSection === "notifications"}
            onClick={() => setActiveSection("notifications")}
            $isCollapsed={isCollapsed}
            whileHover={{ x: 5 }}
            whileTap={{ scale: 0.98 }}
          >
            <IconWrapper $isCollapsed={isCollapsed}>
              <FaBell />
            </IconWrapper>
            <NavText $isCollapsed={isCollapsed}>Notifications</NavText>
          </NavItem>
        </NavItems>
      </Sidebar>

      <ContentArea>
        <AnimatePresence mode="wait">
          {activeSection === "profile" && (
            <AccountSection
              $darkMode={darkMode}
              key="profile"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SectionTitle $darkMode={darkMode}>
                <FaUser /> Profile Information
                {!editMode && (
                  <IconButton
                    $darkMode={darkMode}
                    variant="primary"
                    onClick={handleEditProfile}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaEdit /> Edit Profile
                  </IconButton>
                )}
              </SectionTitle>

              <ProfileImageWrapper>
                <ProfileImage>
                  {profileImage ? (
                    <img src={profileImage} alt="Profile" />
                  ) : (
                    <FaUser size={40} color="#fff" />
                  )}
                </ProfileImage>

                <UploadButton $darkMode={darkMode}>
                  <FaUpload /> Upload Profile Picture
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                  />
                </UploadButton>
              </ProfileImageWrapper>

              {editMode ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <EditableField>
                    <FieldLabel htmlFor="displayName" $darkMode={darkMode}>
                      Full Name
                    </FieldLabel>
                    <FieldInput
                      type="text"
                      id="displayName"
                      name="displayName"
                      value={userProfile.displayName}
                      onChange={handleInputChange}
                      $darkMode={darkMode}
                    />
                  </EditableField>

                  <EditableField>
                    <FieldLabel htmlFor="phoneNumber" $darkMode={darkMode}>
                      Phone Number
                    </FieldLabel>
                    <FieldInput
                      type="tel"
                      id="phoneNumber"
                      name="phoneNumber"
                      value={userProfile.phoneNumber}
                      onChange={handleInputChange}
                      $darkMode={darkMode}
                    />
                  </EditableField>

                  <EditableField>
                    <FieldLabel htmlFor="dateOfBirth" $darkMode={darkMode}>
                      Date of Birth
                    </FieldLabel>
                    <FieldInput
                      type="date"
                      id="dateOfBirth"
                      name="dateOfBirth"
                      value={userProfile.dateOfBirth}
                      onChange={handleInputChange}
                      $darkMode={darkMode}
                    />
                  </EditableField>

                  <EditableField>
                    <FieldLabel htmlFor="gender" $darkMode={darkMode}>
                      Gender
                    </FieldLabel>
                    <FieldInput
                      as="select"
                      id="gender"
                      name="gender"
                      value={userProfile.gender}
                      onChange={handleInputChange}
                      $darkMode={darkMode}
                    >
                      <option value="">Prefer not to say</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="other">Other</option>
                    </FieldInput>
                  </EditableField>

                  <EditableField>
                    <FieldLabel htmlFor="bio" $darkMode={darkMode}>
                      About Me
                    </FieldLabel>
                    <FieldInput
                      as="textarea"
                      id="bio"
                      name="bio"
                      value={userProfile.bio}
                      onChange={handleInputChange}
                      rows="3"
                      $darkMode={darkMode}
                    />
                  </EditableField>

                  <ButtonGroup>
                    <IconButton
                      $darkMode={darkMode}
                      variant="primary"
                      onClick={handleSaveProfile}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      disabled={loading}
                    >
                      <FaSave /> {loading ? "Saving..." : "Save Changes"}
                    </IconButton>
                    <IconButton
                      $darkMode={darkMode}
                      onClick={handleCancelEdit}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaTimes /> Cancel
                    </IconButton>
                  </ButtonGroup>
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <ProfileField>
                    <strong>Email:</strong> {user?.email}
                  </ProfileField>

                  <ProfileField>
                    <strong>Name:</strong>{" "}
                    {userProfile.displayName || "Not set"}
                  </ProfileField>

                  <ProfileField>
                    <strong>Phone:</strong>{" "}
                    {userProfile.phoneNumber || "Not provided"}
                  </ProfileField>

                  <ProfileField>
                    <strong>Birthday:</strong>{" "}
                    {formatDate(userProfile.dateOfBirth)}
                  </ProfileField>

                  <ProfileField>
                    <strong>Gender:</strong>{" "}
                    {userProfile.gender
                      ? userProfile.gender.charAt(0).toUpperCase() +
                        userProfile.gender.slice(1)
                      : "Not provided"}
                  </ProfileField>

                  {userProfile.bio && (
                    <ProfileField style={{ flexDirection: "column" }}>
                      <strong>About Me:</strong>
                      <p style={{ marginTop: spacing.xs }}>{userProfile.bio}</p>
                    </ProfileField>
                  )}

                  <Button
                    onClick={handleLogout}
                    $darkMode={darkMode}
                    style={{ marginTop: spacing.md }}
                  >
                    Sign Out
                  </Button>
                  <div style={{ marginTop: spacing.lg, position: "relative" }}>
                    <motion.div
                      style={{
                        position: "relative",
                        transform: `translate(${deleteButtonPosition.x}px, ${deleteButtonPosition.y}px)`,
                        transition: "transform 0.3s ease-out",
                        display: "inline-block",
                      }}
                    >
                      <IconButton
                        ref={deleteButtonRef}
                        $darkMode={darkMode}
                        variant="error"
                        onClick={handleDeleteAccountAttempt}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        style={{
                          backgroundColor: colors.error.main,
                          color: colors.neutral.white,
                          padding: `${spacing.xs} ${spacing.sm}`,
                          marginTop: spacing.md,
                        }}
                      >
                        <FaTrash />{" "}
                        {deleteAttempts === 0
                          ? "Delete Account"
                          : deleteAttempts < 6
                          ? [
                              "Are you sure?",
                              "Are you ABSOLUTELY sure?",
                              "🤷‍♀️ Maybe this isn't what you meant to do...",
                              "🙏No, please don't leave us! 😢",
                              "We love you! 💔 😭",
                              "Okay fine, you have a right to leave...",
                            ][deleteAttempts - 1]
                          : "Delete Account"}
                      </IconButton>
                    </motion.div>
                  </div>
                </motion.div>
              )}
            </AccountSection>
          )}

          {activeSection === "orders" && (
            <AccountSection
              $darkMode={darkMode}
              key="orders"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SectionTitle $darkMode={darkMode}>
                <FaShoppingBag /> Order History
              </SectionTitle>

              {ordersLoading ? (
                <div style={{ textAlign: "center", padding: spacing.md }}>
                  <p>Loading your orders...</p>
                </div>
              ) : ordersError ? (
                <div
                  style={{
                    textAlign: "center",
                    padding: spacing.md,
                    color: colors.error.main,
                  }}
                >
                  <FaExclamationTriangle
                    size={24}
                    style={{ marginBottom: spacing.sm }}
                  />
                  <p>{ordersError}</p>
                  <Button
                    $darkMode={darkMode}
                    onClick={fetchOrders}
                    style={{ marginTop: spacing.sm }}
                  >
                    Try Again
                  </Button>
                </div>
              ) : orders.length === 0 ? (
                <EmptyState $darkMode={darkMode}>
                  <FaShoppingBag size={40} />
                  <p>You haven't placed any orders yet.</p>
                  <Button
                    as="a"
                    href="/shop"
                    $darkMode={darkMode}
                    style={{ display: "inline-block", marginTop: spacing.md }}
                  >
                    Start Shopping
                  </Button>
                </EmptyState>
              ) : (
                <>
                  <div style={{ overflowX: "auto" }}>
                    <OrdersTable>
                      <thead>
                        <tr>
                          <OrdersTh $darkMode={darkMode}>
                            Order ID / Date
                          </OrdersTh>
                          <OrdersTh $darkMode={darkMode}>Items</OrdersTh>
                          <OrdersTh $darkMode={darkMode}>Total</OrdersTh>
                          <OrdersTh $darkMode={darkMode}>Status</OrdersTh>
                          <OrdersTh $darkMode={darkMode}>Actions</OrdersTh>
                        </tr>
                      </thead>
                      <tbody>
                        {orders.map((order) => (
                          <tr key={order.id}>
                            <OrdersTd $darkMode={darkMode}>
                              <div>
                                <div
                                  style={{
                                    fontWeight: typography.fontWeight.medium,
                                  }}
                                >
                                  {order.id}
                                </div>
                                <div
                                  style={{
                                    fontSize: typography.fontSize.sm,
                                    color: darkMode
                                      ? colors.neutral.light
                                      : colors.neutral.gray,
                                  }}
                                >
                                  {formatDate(order.createdAt)}
                                </div>
                              </div>
                            </OrdersTd>
                            <OrdersTd $darkMode={darkMode}>
                              {order.items?.length || 0} items
                            </OrdersTd>
                            <OrdersTd $darkMode={darkMode}>
                              {formatCurrency(order.total)}
                            </OrdersTd>
                            <OrdersTd $darkMode={darkMode}>
                              <OrderStatusBadge status={order.status}>
                                {order.status}
                              </OrderStatusBadge>
                            </OrdersTd>
                            <OrdersTd $darkMode={darkMode}>
                              <div style={{ display: "flex" }}>
                                <OrderActionButton
                                  $darkMode={darkMode}
                                  title="View Details"
                                  onClick={() => {
                                    setSelectedOrder(order);
                                    setShowOrderDetails(true);
                                  }}
                                  small
                                  iconOnly
                                >
                                  <FaEye />
                                </OrderActionButton>

                                {order.status !== "cancelled" &&
                                  order.status !== "delivered" &&
                                  order.status !== "refunded" &&
                                  order.status !== "shipped" && (
                                    <OrderActionButton
                                      $darkMode={darkMode}
                                      title="Cancel Order"
                                      onClick={() =>
                                        handleCancelOrder(order.id)
                                      }
                                      small
                                      iconOnly
                                    >
                                      <FaTrash />
                                    </OrderActionButton>
                                  )}

                                <OrderActionButton
                                  $darkMode={darkMode}
                                  title="Order Again"
                                  onClick={() => handleReorder(order)}
                                  small
                                  iconOnly
                                >
                                  <FaRedo />
                                </OrderActionButton>
                              </div>
                            </OrdersTd>
                          </tr>
                        ))}
                      </tbody>
                    </OrdersTable>
                  </div>

                  {totalUserOrders > ordersPerPage && (
                    <OrderPagination>
                      <OrderPageButton
                        $darkMode={darkMode}
                        onClick={() => setCurrentOrderPage(1)}
                        disabled={currentOrderPage === 1}
                      >
                        First
                      </OrderPageButton>
                      <OrderPageButton
                        $darkMode={darkMode}
                        onClick={() =>
                          setCurrentOrderPage((prev) => Math.max(prev - 1, 1))
                        }
                        disabled={currentOrderPage === 1}
                      >
                        Previous
                      </OrderPageButton>
                      <OrderPageButton $darkMode={darkMode} active>
                        {currentOrderPage}
                      </OrderPageButton>
                      <OrderPageButton
                        $darkMode={darkMode}
                        onClick={() => setCurrentOrderPage((prev) => prev + 1)}
                        disabled={
                          currentOrderPage * ordersPerPage >= totalUserOrders
                        }
                      >
                        Next
                      </OrderPageButton>
                    </OrderPagination>
                  )}
                </>
              )}

              {/* Order Details Modal */}
              {selectedOrder && (
                <Modal
                  isOpen={showOrderDetails}
                  onClose={() => {
                    setShowOrderDetails(false);
                    setSelectedOrder(null);
                  }}
                  title={`Order Details: ${selectedOrder.id}`}
                  $darkMode={darkMode}
                >
                  <div style={{ marginBottom: spacing.md }}>
                    <strong>Date:</strong> {formatDate(selectedOrder.createdAt)}
                  </div>

                  <div style={{ marginBottom: spacing.md }}>
                    <strong>Status:</strong>{" "}
                    <OrderStatusBadge status={selectedOrder.status}>
                      {selectedOrder.status}
                    </OrderStatusBadge>
                  </div>

                  <div style={{ marginBottom: spacing.md }}>
                    <strong>Shipping Address:</strong>
                    <div>{selectedOrder.shipping?.name}</div>
                    <div>{selectedOrder.shipping?.address}</div>
                    <div>
                      {selectedOrder.shipping?.city},{" "}
                      {selectedOrder.shipping?.state}{" "}
                      {selectedOrder.shipping?.zip}
                    </div>
                    <div>{selectedOrder.shipping?.country}</div>
                  </div>

                  <div style={{ marginBottom: spacing.md }}>
                    <strong>Items:</strong>
                    <div style={{ marginTop: spacing.xs }}>
                      {selectedOrder.items?.map((item, index) => (
                        <div
                          key={index}
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            marginBottom: spacing.xs,
                            padding: spacing.xs,
                            borderBottom: `1px solid ${
                              darkMode
                                ? colors.neutral.dark
                                : colors.neutral.light
                            }`,
                          }}
                        >
                          <div>
                            {item.quantity} x {item.name}
                          </div>
                          <div>
                            {formatCurrency(item.price * item.quantity)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div
                    style={{
                      marginTop: spacing.md,
                      borderTop: `1px solid ${
                        darkMode ? colors.neutral.dark : colors.neutral.light
                      }`,
                      paddingTop: spacing.md,
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <strong>Subtotal:</strong>
                      <div>{formatCurrency(selectedOrder.subtotal)}</div>
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <strong>Shipping:</strong>
                      <div>{formatCurrency(selectedOrder.shipping)}</div>
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <strong>Tax:</strong>
                      <div>{formatCurrency(selectedOrder.tax)}</div>
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        marginTop: spacing.sm,
                        fontWeight: typography.fontWeight.bold,
                      }}
                    >
                      <strong>Total:</strong>
                      <div>{formatCurrency(selectedOrder.total)}</div>
                    </div>
                  </div>

                  <div
                    style={{
                      marginTop: spacing.lg,
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    {selectedOrder.status !== "cancelled" &&
                      selectedOrder.status !== "delivered" &&
                      selectedOrder.status !== "refunded" &&
                      selectedOrder.status !== "shipped" && (
                        <Button
                          $darkMode={darkMode}
                          onClick={() => handleCancelOrder(selectedOrder.id)}
                          style={{ backgroundColor: colors.error.main }}
                        >
                          Cancel Order
                        </Button>
                      )}
                    <Button
                      $darkMode={darkMode}
                      onClick={() => handleReorder(selectedOrder)}
                    >
                      Order Again
                    </Button>
                  </div>
                </Modal>
              )}
            </AccountSection>
          )}

          {activeSection === "wishlist" && (
            <AccountSection
              $darkMode={darkMode}
              key="wishlist"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SectionTitle $darkMode={darkMode}>
                <FaHeart /> My Wishlist
              </SectionTitle>

              {wishlistLoading ? (
                <p>Loading wishlist...</p>
              ) : wishlistItems.length === 0 ? (
                <EmptyState $darkMode={darkMode}>
                  <FaHeart size={40} />
                  <p>Your wishlist is empty.</p>
                  <Button
                    as="a"
                    href="/shop"
                    $darkMode={darkMode}
                    style={{ display: "inline-block", marginTop: spacing.md }}
                  >
                    Explore Products
                  </Button>
                </EmptyState>
              ) : (
                <WishlistGrid>
                  {wishlistItems.map((item) => (
                    <WishlistItem key={item.id} $darkMode={darkMode}>
                      <Link to={`/shop/product/${item.id}`}>
                        <img src={item.imageUrl} alt={item.name} />
                        <div className="details">
                          <WishlistItemName $darkMode={darkMode}>
                            {item.name}
                          </WishlistItemName>
                          <WishlistItemPrice $darkMode={darkMode}>
                            ${item.price.toFixed(2)}
                          </WishlistItemPrice>
                          <WishlistItemActions>
                            <WishlistItemButton
                              primary
                              $darkMode={darkMode}
                              onClick={(e) => {
                                e.preventDefault();
                                handleAddToCart(item);
                              }}
                            >
                              <FaShoppingCart size={14} /> Add to Cart
                            </WishlistItemButton>
                            <WishlistItemButton
                              $darkMode={darkMode}
                              onClick={(e) => {
                                e.preventDefault();
                                removeFromWishlist(item.id);
                              }}
                            >
                              <FaTrash size={14} /> Remove
                            </WishlistItemButton>
                          </WishlistItemActions>
                        </div>
                      </Link>
                    </WishlistItem>
                  ))}
                </WishlistGrid>
              )}
            </AccountSection>
          )}

          {activeSection === "addresses" && (
            <AccountSection
              $darkMode={darkMode}
              key="addresses"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SectionTitle $darkMode={darkMode}>
                <FaAddressCard /> Saved Addresses
                <IconButton
                  $darkMode={darkMode}
                  variant="primary"
                  onClick={() => setShowAddressForm(!showAddressForm)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {showAddressForm ? <FaTimes /> : <FaPlus />}{" "}
                  {showAddressForm ? "Cancel" : "Add Address"}
                </IconButton>
              </SectionTitle>

              {/* Address Form */}
              {showAddressForm && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <EditableField>
                    <FieldLabel htmlFor="name" $darkMode={darkMode}>
                      Full Name
                    </FieldLabel>
                    <FieldInput
                      type="text"
                      id="name"
                      name="name"
                      value={newAddress.name}
                      onChange={handleAddressChange}
                      $darkMode={darkMode}
                    />
                  </EditableField>

                  <EditableField>
                    <FieldLabel htmlFor="street" $darkMode={darkMode}>
                      Street Address
                    </FieldLabel>
                    <FieldInput
                      type="text"
                      id="street"
                      name="street"
                      value={newAddress.street}
                      onChange={handleAddressChange}
                      $darkMode={darkMode}
                    />
                  </EditableField>

                  <FormRow>
                    <EditableField>
                      <FieldLabel htmlFor="city" $darkMode={darkMode}>
                        City
                      </FieldLabel>
                      <FieldInput
                        type="text"
                        id="city"
                        name="city"
                        value={newAddress.city}
                        onChange={handleAddressChange}
                        $darkMode={darkMode}
                      />
                    </EditableField>

                    <EditableField>
                      <FieldLabel htmlFor="state" $darkMode={darkMode}>
                        State/Province
                      </FieldLabel>
                      <FieldInput
                        type="text"
                        id="state"
                        name="state"
                        value={newAddress.state}
                        onChange={handleAddressChange}
                        $darkMode={darkMode}
                      />
                    </EditableField>
                  </FormRow>

                  <FormRow>
                    <EditableField>
                      <FieldLabel htmlFor="zip" $darkMode={darkMode}>
                        ZIP/Postal Code
                      </FieldLabel>
                      <FieldInput
                        type="text"
                        id="zip"
                        name="zip"
                        value={newAddress.zip}
                        onChange={handleAddressChange}
                        $darkMode={darkMode}
                      />
                    </EditableField>

                    <EditableField>
                      <FieldLabel htmlFor="country" $darkMode={darkMode}>
                        Country
                      </FieldLabel>
                      <FieldInput
                        as="select"
                        id="country"
                        name="country"
                        value={newAddress.country}
                        onChange={handleAddressChange}
                        $darkMode={darkMode}
                      >
                        <option value="US">United States</option>
                        <option value="CA">Canada</option>
                        <option value="UK">United Kingdom</option>
                        <option value="AU">Australia</option>
                      </FieldInput>
                    </EditableField>
                  </FormRow>

                  <CheckboxLabel $darkMode={darkMode}>
                    <input
                      type="checkbox"
                      id="isDefault"
                      name="isDefault"
                      checked={newAddress.isDefault}
                      onChange={handleAddressChange}
                    />
                    Set as default address
                  </CheckboxLabel>

                  <ButtonGroup>
                    <IconButton
                      $darkMode={darkMode}
                      variant="primary"
                      onClick={handleAddAddress}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaSave /> Save Address
                    </IconButton>
                  </ButtonGroup>
                </motion.div>
              )}

              {/* Display Addresses */}
              {addresses.length > 0 ? (
                <AddressGrid>
                  {addresses.map((address) => (
                    <AddressCard key={address.id} $darkMode={darkMode}>
                      {address.isDefault && (
                        <DefaultBadge>Default</DefaultBadge>
                      )}
                      <strong>{address.name}</strong>
                      <p>{address.street}</p>
                      <p>
                        {address.city}, {address.state} {address.zip}
                      </p>
                      <p>{address.country}</p>
                      <ButtonGroup style={{ marginTop: spacing.sm }}>
                        <IconButton
                          $darkMode={darkMode}
                          onClick={() => handleEditAddress(address.id)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          small
                          iconOnly
                        >
                          <FaEdit />
                        </IconButton>
                        <IconButton
                          $darkMode={darkMode}
                          onClick={() => handleDeleteAddress(address.id)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          small
                          iconOnly
                        >
                          <FaTrash />
                        </IconButton>
                        {!address.isDefault && (
                          <IconButton
                            $darkMode={darkMode}
                            onClick={() => handleSetDefaultAddress(address.id)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            small
                            iconOnly
                          >
                            <FaStar />
                          </IconButton>
                        )}
                      </ButtonGroup>
                    </AddressCard>
                  ))}
                </AddressGrid>
              ) : (
                !showAddressForm && (
                  <EmptyState $darkMode={darkMode}>
                    <FaAddressCard size={40} />
                    <p>You don't have any saved addresses.</p>
                    <Button
                      $darkMode={darkMode}
                      style={{ display: "inline-block", marginTop: spacing.md }}
                      onClick={() => setShowAddressForm(true)}
                    >
                      Add New Address
                    </Button>
                  </EmptyState>
                )
              )}
            </AccountSection>
          )}

          {activeSection === "payment" && (
            <AccountSection
              $darkMode={darkMode}
              key="payment"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SectionTitle $darkMode={darkMode}>
                <FaCreditCard /> Payment Methods
              </SectionTitle>
              <p style={{ marginBottom: spacing.md }}>
                Your payment methods are securely stored with Stripe. You can
                add a new payment method during checkout by selecting the "Save
                this payment method" option.
              </p>
              <p>
                For security reasons, we don't store your complete card
                information. Your saved payment methods will appear during
                checkout for easy selection.
              </p>
              <div style={{ marginTop: spacing.lg }}>
                <Button
                  as="a"
                  href="/checkout"
                  $darkMode={darkMode}
                  style={{ display: "inline-block" }}
                >
                  Go to Checkout
                </Button>
              </div>
            </AccountSection>
          )}

          {activeSection === "notifications" && (
            <AccountSection
              $darkMode={darkMode}
              key="notifications"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SectionTitle $darkMode={darkMode}>
                <FaBell /> Notifications
              </SectionTitle>
              <EmptyState $darkMode={darkMode}>
                <FaBell size={40} />
                <p>You don't have any notifications.</p>
                <Button
                  $darkMode={darkMode}
                  style={{ display: "inline-block", marginTop: spacing.md }}
                >
                  Enable Notifications
                </Button>
              </EmptyState>
            </AccountSection>
          )}
        </AnimatePresence>
      </ContentArea>
      {/* Address Modal */}
      <Modal
        isOpen={showAddressModal}
        onClose={() => setShowAddressModal(false)}
        title="Add New Address"
        $darkMode={darkMode}
      >
        <EditableField>
          <FieldLabel htmlFor="name" $darkMode={darkMode}>
            Full Name
          </FieldLabel>
          <FieldInput
            type="text"
            id="name"
            name="name"
            value={newAddress.name}
            onChange={handleAddressChange}
            $darkMode={darkMode}
          />
        </EditableField>

        <EditableField>
          <FieldLabel htmlFor="street" $darkMode={darkMode}>
            Street Address
          </FieldLabel>
          <FieldInput
            type="text"
            id="street"
            name="street"
            value={newAddress.street}
            onChange={handleAddressChange}
            $darkMode={darkMode}
          />
        </EditableField>

        <EditableField>
          <FieldLabel htmlFor="city" $darkMode={darkMode}>
            City
          </FieldLabel>
          <FieldInput
            type="text"
            id="city"
            name="city"
            value={newAddress.city}
            onChange={handleAddressChange}
            $darkMode={darkMode}
          />
        </EditableField>

        <EditableField>
          <FieldLabel htmlFor="state" $darkMode={darkMode}>
            State/Province
          </FieldLabel>
          <FieldInput
            type="text"
            id="state"
            name="state"
            value={newAddress.state}
            onChange={handleAddressChange}
            $darkMode={darkMode}
          />
        </EditableField>

        <EditableField>
          <FieldLabel htmlFor="zip" $darkMode={darkMode}>
            ZIP/Postal Code
          </FieldLabel>
          <FieldInput
            type="text"
            id="zip"
            name="zip"
            value={newAddress.zip}
            onChange={handleAddressChange}
            $darkMode={darkMode}
          />
        </EditableField>

        <EditableField>
          <FieldLabel htmlFor="country" $darkMode={darkMode}>
            Country
          </FieldLabel>
          <FieldInput
            as="select"
            id="country"
            name="country"
            value={newAddress.country}
            onChange={handleAddressChange}
            $darkMode={darkMode}
          >
            <option value="US">United States</option>
            <option value="CA">Canada</option>
            <option value="UK">United Kingdom</option>
            <option value="AU">Australia</option>
          </FieldInput>
        </EditableField>

        <EditableField style={{ flexDirection: "row", alignItems: "center" }}>
          <input
            type="checkbox"
            id="isDefault"
            name="isDefault"
            checked={newAddress.isDefault}
            onChange={handleAddressChange}
            style={{ marginRight: spacing.sm }}
          />
          <FieldLabel
            htmlFor="isDefault"
            $darkMode={darkMode}
            style={{ margin: 0 }}
          >
            Set as default address
          </FieldLabel>
        </EditableField>

        <ButtonGroup>
          <IconButton
            $darkMode={darkMode}
            variant="primary"
            onClick={handleAddAddress}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FaSave /> Save Address
          </IconButton>
          <IconButton
            $darkMode={darkMode}
            onClick={() => setShowAddressModal(false)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FaTimes /> Cancel
          </IconButton>
        </ButtonGroup>
      </Modal>
      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteAccountModal}
        onClose={() => {
          setDeleteAccountModal(false);
          setDeleteAttempts(0);
          setDeleteButtonPosition({ x: 0, y: 0 });
        }}
        onConfirm={handleDeleteAccount}
        itemName="your account"
        darkMode={darkMode}
      />
    </AccountContainer>
  );
};

export default Account;
