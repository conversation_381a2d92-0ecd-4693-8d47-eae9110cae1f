import { createContext, useContext, useState, useEffect } from "react";
import { getStorageItem, setStorageItem } from "../utils/localStorage";
import { functions } from "../firebase/config";
import { httpsCallable } from "firebase/functions";
import { useAuth } from "./AuthContext";

const CartContext = createContext();

export const useCart = () => {
  return useContext(CartContext);
};

export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);
  const [cartCount, setCartCount] = useState(0);
  const [cartTotal, setCartTotal] = useState(0);
  const { currentUser } = useAuth();

  // Track cart abandonment
  useEffect(() => {
    let abandonmentTimer;

    const trackAbandonedCart = async () => {
      if (cartItems.length > 0) {
        try {
          const trackAbandonedCartFn = httpsCallable(
            functions,
            "trackAbandonedCart"
          );

          await trackAbandonedCartFn({
            cartItems: cartItems,
            userEmail: currentUser?.email || null,
            userId: currentUser?.uid || null,
          });

          console.log("Abandoned cart tracked");
        } catch (error) {
          console.error("Error tracking abandoned cart:", error);
        }
      }
    };

    // Clear existing timer
    if (abandonmentTimer) {
      clearTimeout(abandonmentTimer);
    }

    // Set new timer for 30 minutes of inactivity
    if (cartItems.length > 0) {
      abandonmentTimer = setTimeout(trackAbandonedCart, 30 * 60 * 1000); // 30 minutes
    }

    return () => {
      if (abandonmentTimer) {
        clearTimeout(abandonmentTimer);
      }
    };
  }, [cartItems, currentUser]);

  useEffect(() => {
    // Load cart from localStorage on initial render
    const savedCart = getStorageItem("cart", []);
    if (savedCart) {
      setCartItems(savedCart);
    }
  }, []);

  useEffect(() => {
    // Update cart count and total when items change
    const count = cartItems.reduce((total, item) => total + item.quantity, 0);
    const total = cartItems.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    setCartCount(count);
    setCartTotal(total);

    // Save to localStorage
    setStorageItem("cart", cartItems);
  }, [cartItems]);

  const addToCart = (product, quantity = 1) => {
    setCartItems((prevItems) => {
      // For products with variants, we need to check if the same variant is already in the cart
      if (product.variant) {
        const existingItemWithVariant = prevItems.find(
          (item) =>
            item.id === product.id &&
            item.variant &&
            item.variant.id === product.variant.id
        );

        if (existingItemWithVariant) {
          // Update quantity if the same variant already exists
          return prevItems.map((item) =>
            item.id === product.id &&
            item.variant &&
            item.variant.id === product.variant.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          );
        } else {
          // Add new item with variant
          return [...prevItems, { ...product, quantity }];
        }
      } else {
        // Regular product without variants
        const existingItem = prevItems.find(
          (item) => item.id === product.id && !item.variant
        );

        if (existingItem) {
          // Update quantity if item already exists
          return prevItems.map((item) =>
            item.id === product.id && !item.variant
              ? { ...item, quantity: item.quantity + quantity }
              : item
          );
        } else {
          // Add new item
          return [...prevItems, { ...product, quantity }];
        }
      }
    });
  };

  const updateQuantity = (productId, quantity, variantId = null) => {
    if (quantity < 1) return;

    setCartItems((prevItems) =>
      prevItems.map((item) => {
        // For items with variants, check both product ID and variant ID
        if (variantId && item.variant) {
          return item.id === productId && item.variant.id === variantId
            ? { ...item, quantity }
            : item;
        } else {
          // For regular items or when variantId is not provided
          return item.id === productId ? { ...item, quantity } : item;
        }
      })
    );
  };

  const removeFromCart = (productId, variantId = null) => {
    setCartItems((prevItems) => {
      if (variantId) {
        // Remove specific variant
        return prevItems.filter(
          (item) =>
            !(
              item.id === productId &&
              item.variant &&
              item.variant.id === variantId
            )
        );
      } else {
        // Remove all items with this product ID
        return prevItems.filter((item) => item.id !== productId);
      }
    });
  };

  const clearCart = () => {
    setCartItems([]);
  };

  const updateCartItemsOrder = (newOrderedItems) => {
    setCartItems(newOrderedItems);
  };

  const value = {
    cartItems,
    cartCount,
    cartTotal,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    updateCartItemsOrder,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};
