import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaEnvelope,
  FaPlus,
  FaEye,
  FaFilter,
  FaDownload,
  FaCheck,
  FaTimes,
  FaClock,
  FaExclamationTriangle,
  FaTrash,
} from "react-icons/fa";
import { colors, spacing, breakpoints } from "../../styles";
import { useAuth } from "../../context/AuthContext";
import { httpsCallable } from "firebase/functions";
import { functions } from "../../firebase/config";
import EmailComposer from "./EmailComposer";
import EmailTester from "./EmailTester";

const Container = styled.div`
  padding: ${spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: ${spacing.xl};
  flex-wrap: wrap;
  gap: ${spacing.md};

  @media (max-width: ${breakpoints.md}) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const Title = styled.h1`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${spacing.md};
`;

const Controls = styled.div`
  display: flex;
  gap: ${spacing.md};
  align-items: center;
  flex-wrap: wrap;
`;

const Button = styled(motion.button).withConfig({
  shouldForwardProp: (prop) => !["variant"].includes(prop),
})`
  background: ${(props) =>
    props.variant === "primary"
      ? `linear-gradient(135deg, ${colors.primary.main}, ${colors.primary.dark})`
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.lightest};
  color: ${(props) =>
    props.variant === "primary"
      ? "white"
      : props.darkMode
      ? colors.neutral.light
      : colors.neutral.dark};
  border: none;
  padding: ${spacing.md} ${spacing.lg};
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
`;

const FilterSection = styled.div`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.lg};
  margin-bottom: ${spacing.xl};
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`;

const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${spacing.md};
  margin-bottom: ${spacing.md};
`;

const Select = styled.select`
  padding: ${spacing.md};
  border: 2px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  border-radius: 8px;
  background: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const EmailGrid = styled.div`
  display: grid;
  gap: ${spacing.md};
`;

const EmailCard = styled(motion.div)`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.lg};
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid
    ${(props) =>
      props.status === "sent"
        ? colors.success.main
        : props.status === "failed"
        ? colors.error.main
        : props.status === "logged_only"
        ? colors.warning.main
        : colors.neutral.light};
`;

const EmailHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${spacing.md};
  flex-wrap: wrap;
  gap: ${spacing.sm};
`;

const EmailInfo = styled.div`
  flex: 1;
`;

const EmailTo = styled.div`
  font-weight: 600;
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin-bottom: ${spacing.xs};
`;

const EmailSubject = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 0.9rem;
`;

const EmailMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  flex-wrap: wrap;
`;

const StatusBadge = styled.div`
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${spacing.xs};

  ${(props) =>
    props.status === "sent" &&
    `
    background: ${colors.success.light}20;
    color: ${colors.success.dark};
  `}

  ${(props) =>
    props.status === "failed" &&
    `
    background: ${colors.error.light}20;
    color: ${colors.error.dark};
  `}
  
  ${(props) =>
    props.status === "logged_only" &&
    `
    background: ${colors.warning.light}20;
    color: ${colors.warning.dark};
  `}
  
  ${(props) =>
    props.status === "pending" &&
    `
    background: ${colors.neutral.light}20;
    color: ${colors.neutral.dark};
  `}
`;

const Timestamp = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 0.8rem;
`;

const EmailContent = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 0.9rem;
  line-height: 1.5;
  margin-top: ${spacing.md};
  padding-top: ${spacing.md};
  border-top: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
`;

const LoadingSpinner = styled(motion.div)`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${spacing.xl};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${spacing.xl};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const EmailActions = styled.div`
  display: flex;
  gap: ${spacing.sm};
  margin-top: ${spacing.md};
  padding-top: ${spacing.md};
  border-top: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
`;

const ActionButton = styled(motion.button)`
  padding: ${spacing.xs} ${spacing.sm};
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  transition: all 0.2s ease;

  &.view {
    background: ${colors.primary.main};
    color: white;
    &:hover {
      background: ${colors.primary.dark};
    }
  }

  &.delete {
    background: ${colors.error.main};
    color: white;
    &:hover {
      background: ${colors.error.dark};
    }
  }
`;

// Modal Components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
`;

const ModalContent = styled.div`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
`;

const ModalHeader = styled.div`
  padding: ${spacing.lg};
  border-bottom: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    color: ${(props) =>
      props.darkMode ? colors.neutral.white : colors.neutral.darker};
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  padding: ${spacing.xs};
  border-radius: 4px;

  &:hover {
    background: ${(props) =>
      props.darkMode ? colors.neutral.dark : colors.neutral.light};
  }
`;

const ModalBody = styled.div`
  padding: ${spacing.lg};
  max-height: 60vh;
  overflow-y: auto;
`;

const DetailRow = styled.div`
  margin-bottom: ${spacing.md};
  display: flex;
  flex-direction: column;
  gap: ${spacing.xs};
`;

const DetailLabel = styled.label`
  font-weight: 600;
  color: ${colors.primary.main};
  font-size: 0.875rem;
`;

const DetailValue = styled.span`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darker};
  font-weight: 500;
`;

const DetailContent = styled.div`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.lighter};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darker};
  padding: ${spacing.md};
  border-radius: 8px;
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  max-height: 200px;
  overflow-y: auto;
  line-height: 1.6;
`;

const ErrorMessage = styled.span`
  color: ${colors.error.main};
  font-family: monospace;
  background: ${colors.error.lighter};
  padding: ${spacing.sm};
  border-radius: 4px;
  display: block;
`;

// Pagination Components
const PaginationContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: ${spacing.lg} 0;
  padding: ${spacing.md};
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};

  @media (max-width: ${breakpoints.tablet}) {
    flex-direction: column;
    gap: ${spacing.md};
  }
`;

const PaginationInfo = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 0.875rem;
`;

const PaginationControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
`;

const PageButton = styled(motion.button)`
  padding: ${spacing.xs} ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  background: ${(props) =>
    props.active
      ? colors.primary.main
      : props.darkMode
      ? colors.neutral.darker
      : colors.neutral.white};
  color: ${(props) =>
    props.active
      ? colors.neutral.white
      : props.darkMode
      ? colors.neutral.light
      : colors.neutral.dark};
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  min-width: 36px;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    background: ${(props) =>
      props.active
        ? colors.primary.dark
        : props.darkMode
        ? colors.neutral.dark
        : colors.neutral.lighter};
  }
`;

const PageSizeSelector = styled.select`
  padding: ${spacing.xs} ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
`;

// Bulk Actions Components
const BulkActionsBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${spacing.md};
  background: ${colors.primary.lighter};
  border-radius: 8px;
  margin-bottom: ${spacing.md};
  border: 1px solid ${colors.primary.light};
`;

const BulkInfo = styled.span`
  color: ${colors.primary.dark};
  font-weight: 500;
`;

const BulkActions = styled.div`
  display: flex;
  gap: ${spacing.sm};
`;

const BulkButton = styled(motion.button)`
  padding: ${spacing.xs} ${spacing.sm};
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  font-weight: 500;

  &.danger {
    background: ${colors.error.main};
    color: white;
    &:hover {
      background: ${colors.error.dark};
    }
  }

  &.secondary {
    background: ${colors.neutral.light};
    color: ${colors.neutral.darker};
    &:hover {
      background: ${colors.neutral.main};
    }
  }
`;

const Checkbox = styled.input`
  margin-right: ${spacing.xs};
  cursor: pointer;
`;

// Thread View Components
const ThreadGrid = styled.div`
  display: grid;
  gap: ${spacing.md};
`;

const ThreadCard = styled(motion.div)`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.lg};
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid
    ${(props) =>
      props.status === "resolved"
        ? colors.success.main
        : props.status === "pending"
        ? colors.warning.main
        : props.priority === "urgent"
        ? colors.error.main
        : props.priority === "high"
        ? colors.warning.main
        : colors.primary.main};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
`;

const ThreadHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${spacing.md};
  flex-wrap: wrap;
  gap: ${spacing.sm};
`;

const ThreadInfo = styled.div`
  flex: 1;
`;

const ThreadCustomer = styled.div`
  font-weight: 600;
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin-bottom: ${spacing.xs};
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
`;

const ThreadSubject = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 0.9rem;
  margin-bottom: ${spacing.xs};
`;

const ThreadMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  flex-wrap: wrap;
`;

const ThreadBadge = styled.div`
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${spacing.xs};

  ${(props) =>
    props.type === "status" &&
    props.value === "open" &&
    `
    background: ${colors.primary.light}20;
    color: ${colors.primary.dark};
  `}

  ${(props) =>
    props.type === "status" &&
    props.value === "resolved" &&
    `
    background: ${colors.success.light}20;
    color: ${colors.success.dark};
  `}

  ${(props) =>
    props.type === "status" &&
    props.value === "pending" &&
    `
    background: ${colors.warning.light}20;
    color: ${colors.warning.dark};
  `}

  ${(props) =>
    props.type === "priority" &&
    props.value === "urgent" &&
    `
    background: ${colors.error.light}20;
    color: ${colors.error.dark};
  `}

  ${(props) =>
    props.type === "priority" &&
    props.value === "high" &&
    `
    background: ${colors.warning.light}20;
    color: ${colors.warning.dark};
  `}

  ${(props) =>
    props.type === "unread" &&
    `
    background: ${colors.primary.main};
    color: white;
  `}
`;

const ThreadActions = styled.div`
  display: flex;
  gap: ${spacing.sm};
  align-items: center;
`;

const ThreadActionButton = styled(motion.button)`
  background: transparent;
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.xs};

  &:hover {
    background: ${(props) =>
      props.darkMode ? colors.neutral.dark : colors.neutral.light};
  }
`;

const EmailManager = ({ darkMode }) => {
  const [emails, setEmails] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showComposer, setShowComposer] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState(null);
  const [showEmailDetails, setShowEmailDetails] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [emailsPerPage, setEmailsPerPage] = useState(10);
  const [totalEmails, setTotalEmails] = useState(0);
  const [selectedEmails, setSelectedEmails] = useState([]);
  const [sortBy, setSortBy] = useState("timestamp");
  const [sortOrder, setSortOrder] = useState("desc");
  const [showThreads, setShowThreads] = useState(false);
  const [filters, setFilters] = useState({
    type: "all",
    status: "all",
  });
  const [replyData, setReplyData] = useState(null);
  const [threads, setThreads] = useState({});
  const [selectedThread, setSelectedThread] = useState(null);
  const [showThreadView, setShowThreadView] = useState(false);
  const [threadFilters, setThreadFilters] = useState({
    status: "all", // all, open, resolved, pending
    priority: "all", // all, low, normal, high, urgent
    assignedTo: "all", // all, me, unassigned, specific user
  });
  const { currentUser, isAuthenticated, isAdmin, isEditor } = useAuth();

  useEffect(() => {
    if (isAuthenticated && (isAdmin || isEditor)) {
      loadEmails();
    }
  }, [filters, isAuthenticated, isAdmin, isEditor]);

  const loadEmails = async (page = 1) => {
    try {
      setLoading(true);

      // Check authentication
      if (!isAuthenticated || (!isAdmin && !isEditor)) {
        return;
      }

      // Load emails from Firestore with pagination
      const {
        collection,
        query,
        orderBy,
        limit,
        startAfter,
        getDocs,
        getCountFromServer,
      } = await import("firebase/firestore");
      const { db } = await import("../../firebase/config");

      // Get total count for pagination
      const countQuery = query(collection(db, "email_logs"));
      const countSnapshot = await getCountFromServer(countQuery);
      setTotalEmails(countSnapshot.data().count);

      // Calculate offset for pagination
      const offset = (page - 1) * emailsPerPage;

      // Create paginated query
      let emailQuery = query(
        collection(db, "email_logs"),
        orderBy("timestamp", "desc"),
        limit(emailsPerPage)
      );

      // If not first page, we need to implement proper pagination
      // For now, we'll load all and slice (not ideal for large datasets)
      const allEmailsQuery = query(
        collection(db, "email_logs"),
        orderBy("timestamp", "desc")
      );

      const snapshot = await getDocs(allEmailsQuery);
      const allEmails = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        timestamp:
          doc.data().timestamp?.toDate?.()?.toISOString() ||
          doc.data().timestamp?.toISOString?.() ||
          new Date().toISOString(),
        createdAt:
          doc.data().createdAt?.toDate?.()?.toISOString() ||
          doc.data().createdAt?.toISOString?.() ||
          new Date().toISOString(),
      }));

      // Apply pagination on client side
      const startIndex = (page - 1) * emailsPerPage;
      const endIndex = startIndex + emailsPerPage;
      const paginatedEmails = allEmails.slice(startIndex, endIndex);

      setEmails(paginatedEmails);
      setCurrentPage(page);

      // Generate threads from all emails (not just paginated ones)
      const emailThreads = groupEmailsByThread(allEmails);
      setThreads(emailThreads);
    } catch (error) {
      console.error("Error loading emails:", error);
      // Simple error handling without excessive logging
      if (error.code === "permission-denied") {
        alert("Permission denied: You need admin privileges to view emails.");
      } else {
        alert(`Error loading emails: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const deleteEmail = async (emailId) => {
    if (!window.confirm("Are you sure you want to delete this email log?")) {
      return;
    }

    try {
      const { doc, deleteDoc } = await import("firebase/firestore");
      const { db } = await import("../../firebase/config");

      await deleteDoc(doc(db, "email_logs", emailId));

      // Remove from local state
      setEmails(emails.filter((email) => email.id !== emailId));

      alert("Email deleted successfully");
    } catch (error) {
      console.error("Error deleting email:", error);
      alert("Error deleting email: " + error.message);
    }
  };

  const viewEmailDetails = (email) => {
    setSelectedEmail(email);
    setShowEmailDetails(true);
  };

  const replyToEmail = (email) => {
    // Pre-fill composer with reply data
    setReplyData({
      to: email.to,
      subject: email.subject.startsWith("Re: ")
        ? email.subject
        : `Re: ${email.subject}`,
      originalEmail: email,
      isReply: true,
    });
    setShowComposer(true);
  };

  const groupEmailsByThread = (emails) => {
    // Advanced threading: Group by customer email and subject similarity
    const threads = {};

    emails.forEach((email) => {
      // Create thread key based on customer email and normalized subject
      const customerEmail = email.to.toLowerCase();
      const normalizedSubject = email.subject
        .replace(/^(re:|fwd?:|fw:)\s*/i, "") // Remove reply/forward prefixes
        .toLowerCase()
        .trim();

      // Create thread ID combining customer and subject
      const threadId = `${customerEmail}::${normalizedSubject}`;

      if (!threads[threadId]) {
        threads[threadId] = {
          id: threadId,
          customerEmail,
          subject: normalizedSubject,
          originalSubject: email.subject,
          emails: [],
          lastActivity: email.timestamp,
          status: "open", // open, resolved, pending
          priority: "normal", // low, normal, high, urgent
          assignedTo: null,
          tags: [],
          unreadCount: 0,
        };
      }

      threads[threadId].emails.push(email);

      // Update thread metadata
      if (
        new Date(email.timestamp) > new Date(threads[threadId].lastActivity)
      ) {
        threads[threadId].lastActivity = email.timestamp;
      }

      // Count unread emails (assuming emails without 'read' flag are unread)
      if (!email.read) {
        threads[threadId].unreadCount++;
      }
    });

    // Sort emails within each thread by timestamp (oldest first for conversation flow)
    Object.values(threads).forEach((thread) => {
      thread.emails.sort(
        (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
      );
    });

    return threads;
  };

  // Thread management functions
  const updateThreadStatus = async (threadId, status) => {
    try {
      // Update thread status in local state
      setThreads((prev) => ({
        ...prev,
        [threadId]: {
          ...prev[threadId],
          status,
        },
      }));

      // TODO: Persist thread metadata to Firestore
      console.log(`Thread ${threadId} status updated to ${status}`);
    } catch (error) {
      console.error("Error updating thread status:", error);
    }
  };

  const assignThread = async (threadId, assignedTo) => {
    try {
      setThreads((prev) => ({
        ...prev,
        [threadId]: {
          ...prev[threadId],
          assignedTo,
        },
      }));

      console.log(`Thread ${threadId} assigned to ${assignedTo}`);
    } catch (error) {
      console.error("Error assigning thread:", error);
    }
  };

  const addThreadTag = async (threadId, tag) => {
    try {
      setThreads((prev) => ({
        ...prev,
        [threadId]: {
          ...prev[threadId],
          tags: [...(prev[threadId].tags || []), tag],
        },
      }));
    } catch (error) {
      console.error("Error adding thread tag:", error);
    }
  };

  const markThreadAsRead = async (threadId) => {
    try {
      setThreads((prev) => ({
        ...prev,
        [threadId]: {
          ...prev[threadId],
          unreadCount: 0,
        },
      }));

      // Mark all emails in thread as read
      const thread = threads[threadId];
      if (thread) {
        // TODO: Update email read status in Firestore
        console.log(`Thread ${threadId} marked as read`);
      }
    } catch (error) {
      console.error("Error marking thread as read:", error);
    }
  };

  const openThreadView = (thread) => {
    setSelectedThread(thread);
    setShowThreadView(true);
    markThreadAsRead(thread.id);
  };

  const toggleEmailSelection = (emailId) => {
    setSelectedEmails((prev) =>
      prev.includes(emailId)
        ? prev.filter((id) => id !== emailId)
        : [...prev, emailId]
    );
  };

  const selectAllEmails = () => {
    if (selectedEmails.length === emails.length) {
      setSelectedEmails([]);
    } else {
      setSelectedEmails(emails.map((email) => email.id));
    }
  };

  const bulkDeleteEmails = async () => {
    if (selectedEmails.length === 0) return;

    if (
      !window.confirm(
        `Are you sure you want to delete ${selectedEmails.length} emails?`
      )
    ) {
      return;
    }

    try {
      const { doc, deleteDoc } = await import("firebase/firestore");
      const { db } = await import("../../firebase/config");

      // Delete all selected emails
      await Promise.all(
        selectedEmails.map((emailId) =>
          deleteDoc(doc(db, "email_logs", emailId))
        )
      );

      // Remove from local state
      setEmails(emails.filter((email) => !selectedEmails.includes(email.id)));
      setSelectedEmails([]);

      alert(`${selectedEmails.length} emails deleted successfully`);
    } catch (error) {
      console.error("Error deleting emails:", error);
      alert("Error deleting emails: " + error.message);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "sent":
        return <FaCheck />;
      case "failed":
        return <FaTimes />;
      case "logged_only":
        return <FaExclamationTriangle />;
      default:
        return <FaClock />;
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return "Unknown";
    return new Date(timestamp).toLocaleString();
  };

  const filteredEmails = emails.filter((email) => {
    if (filters.status !== "all" && email.status !== filters.status)
      return false;
    if (filters.type !== "all" && email.type !== filters.type) return false;
    return true;
  });

  return (
    <Container>
      <Header>
        <Title darkMode={darkMode}>
          <FaEnvelope />
          Email Management
        </Title>
        <Controls>
          <Button
            variant="primary"
            onClick={() => setShowComposer(true)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <FaPlus /> Compose Email
          </Button>
          <Button
            darkMode={darkMode}
            onClick={() => setShowThreads(!showThreads)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {showThreads ? "📧 Email View" : "🧵 Thread View"}
          </Button>
          <Button
            darkMode={darkMode}
            onClick={() => {
              setCurrentPage(1);
              loadEmails(1);
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <FaDownload /> Refresh
          </Button>
        </Controls>
      </Header>

      {/* Email Composer Section */}
      <EmailComposer
        isOpen={showComposer}
        onClose={() => {
          setShowComposer(false);
          setReplyData(null);
        }}
        darkMode={darkMode}
        replyData={replyData}
      />

      {/* Email Tester Section */}
      <EmailTester darkMode={darkMode} />

      {/* Filter Section - positioned above email list */}
      <FilterSection darkMode={darkMode}>
        <FilterGrid>
          <div>
            <label
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: spacing.sm,
                color: darkMode ? colors.neutral.light : colors.neutral.dark,
                fontWeight: 500,
                cursor: "pointer",
              }}
            >
              <Checkbox
                type="checkbox"
                checked={
                  filteredEmails.length > 0 &&
                  selectedEmails.length === filteredEmails.length
                }
                onChange={selectAllEmails}
              />
              Select All ({filteredEmails.length})
            </label>
          </div>
          <div>
            <label
              style={{
                display: "block",
                marginBottom: spacing.sm,
                color: darkMode ? colors.neutral.light : colors.neutral.dark,
                fontWeight: 500,
              }}
            >
              Email Type
            </label>
            <Select
              darkMode={darkMode}
              value={filters.type}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, type: e.target.value }))
              }
            >
              <option value="all">All Types</option>
              <option value="general">General</option>
              <option value="order">Order Confirmations</option>
              <option value="support">Support</option>
              <option value="welcome">Welcome Emails</option>
            </Select>
          </div>
          <div>
            <label
              style={{
                display: "block",
                marginBottom: spacing.sm,
                color: darkMode ? colors.neutral.light : colors.neutral.dark,
                fontWeight: 500,
              }}
            >
              Status
            </label>
            <Select
              darkMode={darkMode}
              value={filters.status}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, status: e.target.value }))
              }
            >
              <option value="all">All Statuses</option>
              <option value="sent">Sent</option>
              <option value="logged_only">Logged Only</option>
              <option value="failed">Failed</option>
              <option value="pending">Pending</option>
            </Select>
          </div>
        </FilterGrid>
      </FilterSection>

      {/* Bulk Actions Bar */}
      {selectedEmails.length > 0 && (
        <BulkActionsBar>
          <BulkInfo>
            {selectedEmails.length} email
            {selectedEmails.length !== 1 ? "s" : ""} selected
          </BulkInfo>
          <BulkActions>
            <BulkButton
              className="secondary"
              onClick={() => setSelectedEmails([])}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Clear Selection
            </BulkButton>
            {isAdmin && (
              <BulkButton
                className="danger"
                onClick={bulkDeleteEmails}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FaTrash /> Delete Selected
              </BulkButton>
            )}
          </BulkActions>
        </BulkActionsBar>
      )}

      {loading ? (
        <LoadingSpinner darkMode={darkMode}>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
          >
            <FaClock />
          </motion.div>
          Loading emails...
        </LoadingSpinner>
      ) : showThreads ? (
        // Thread View
        Object.keys(threads).length === 0 ? (
          <EmptyState darkMode={darkMode}>
            <FaEnvelope
              size={48}
              style={{ marginBottom: spacing.md, opacity: 0.5 }}
            />
            <h3>No email threads found</h3>
            <p>No email conversations match your current filters.</p>
          </EmptyState>
        ) : (
          <ThreadGrid>
            <AnimatePresence>
              {Object.values(threads)
                .filter((thread) => {
                  if (
                    threadFilters.status !== "all" &&
                    thread.status !== threadFilters.status
                  )
                    return false;
                  if (
                    threadFilters.priority !== "all" &&
                    thread.priority !== threadFilters.priority
                  )
                    return false;
                  return true;
                })
                .sort(
                  (a, b) => new Date(b.lastActivity) - new Date(a.lastActivity)
                )
                .map((thread, index) => (
                  <ThreadCard
                    key={thread.id}
                    darkMode={darkMode}
                    status={thread.status}
                    priority={thread.priority}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => openThreadView(thread)}
                  >
                    <ThreadHeader>
                      <ThreadInfo>
                        <ThreadCustomer darkMode={darkMode}>
                          {thread.customerEmail}
                          {thread.unreadCount > 0 && (
                            <ThreadBadge type="unread">
                              {thread.unreadCount} new
                            </ThreadBadge>
                          )}
                        </ThreadCustomer>
                        <ThreadSubject darkMode={darkMode}>
                          {thread.originalSubject}
                        </ThreadSubject>
                        <ThreadMeta>
                          <ThreadBadge type="status" value={thread.status}>
                            {thread.status}
                          </ThreadBadge>
                          {thread.priority !== "normal" && (
                            <ThreadBadge
                              type="priority"
                              value={thread.priority}
                            >
                              {thread.priority}
                            </ThreadBadge>
                          )}
                          <div
                            style={{
                              fontSize: "0.8rem",
                              color: darkMode
                                ? colors.neutral.light
                                : colors.neutral.dark,
                            }}
                          >
                            {thread.emails.length} message
                            {thread.emails.length !== 1 ? "s" : ""} • Last:{" "}
                            {formatTimestamp(thread.lastActivity)}
                          </div>
                        </ThreadMeta>
                      </ThreadInfo>
                      <ThreadActions>
                        <ThreadActionButton
                          darkMode={darkMode}
                          onClick={(e) => {
                            e.stopPropagation();
                            updateThreadStatus(
                              thread.id,
                              thread.status === "resolved" ? "open" : "resolved"
                            );
                          }}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          {thread.status === "resolved"
                            ? "🔄 Reopen"
                            : "✅ Resolve"}
                        </ThreadActionButton>
                      </ThreadActions>
                    </ThreadHeader>
                  </ThreadCard>
                ))}
            </AnimatePresence>
          </ThreadGrid>
        )
      ) : filteredEmails.length === 0 ? (
        <EmptyState darkMode={darkMode}>
          <FaEnvelope
            size={48}
            style={{ marginBottom: spacing.md, opacity: 0.5 }}
          />
          <h3>No emails found</h3>
          <p>No emails match your current filters.</p>
        </EmptyState>
      ) : (
        <EmailGrid>
          <AnimatePresence>
            {filteredEmails.map((email, index) => (
              <EmailCard
                key={email.id}
                darkMode={darkMode}
                status={email.status}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <EmailHeader>
                  <Checkbox
                    type="checkbox"
                    checked={selectedEmails.includes(email.id)}
                    onChange={() => toggleEmailSelection(email.id)}
                  />
                  <EmailInfo>
                    <EmailTo darkMode={darkMode}>To: {email.to}</EmailTo>
                    <EmailSubject darkMode={darkMode}>
                      {email.subject}
                    </EmailSubject>
                  </EmailInfo>
                  <EmailMeta>
                    <StatusBadge status={email.status}>
                      {getStatusIcon(email.status)}
                      {email.status}
                    </StatusBadge>
                    <Timestamp darkMode={darkMode}>
                      {formatTimestamp(email.timestamp)}
                    </Timestamp>
                  </EmailMeta>
                </EmailHeader>
                {email.content && (
                  <EmailContent darkMode={darkMode}>
                    {(() => {
                      // Strip HTML tags for preview
                      const textContent = email.content.replace(/<[^>]*>/g, "");
                      return textContent.length > 200
                        ? `${textContent.substring(0, 200)}...`
                        : textContent;
                    })()}
                  </EmailContent>
                )}

                <EmailActions darkMode={darkMode}>
                  <ActionButton
                    className="view"
                    onClick={() => viewEmailDetails(email)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaEye /> View Details
                  </ActionButton>
                  {isAdmin && (
                    <ActionButton
                      className="delete"
                      onClick={() => deleteEmail(email.id)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaTrash /> Delete
                    </ActionButton>
                  )}
                </EmailActions>
              </EmailCard>
            ))}
          </AnimatePresence>
        </EmailGrid>
      )}

      {/* Pagination */}
      {emails.length > 0 && (
        <PaginationContainer darkMode={darkMode}>
          <PaginationInfo darkMode={darkMode}>
            Showing {(currentPage - 1) * emailsPerPage + 1} to{" "}
            {Math.min(currentPage * emailsPerPage, totalEmails)} of{" "}
            {totalEmails} emails
          </PaginationInfo>

          <PaginationControls>
            <PageSizeSelector
              darkMode={darkMode}
              value={emailsPerPage}
              onChange={(e) => {
                setEmailsPerPage(Number(e.target.value));
                setCurrentPage(1);
                loadEmails(1);
              }}
            >
              <option value={5}>5 per page</option>
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
            </PageSizeSelector>

            <PageButton
              darkMode={darkMode}
              disabled={currentPage === 1}
              onClick={() => loadEmails(currentPage - 1)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Previous
            </PageButton>

            {/* Page numbers */}
            {Array.from(
              { length: Math.ceil(totalEmails / emailsPerPage) },
              (_, i) => i + 1
            )
              .filter((page) => {
                const totalPages = Math.ceil(totalEmails / emailsPerPage);
                if (totalPages <= 7) return true;
                if (page === 1 || page === totalPages) return true;
                if (page >= currentPage - 2 && page <= currentPage + 2)
                  return true;
                return false;
              })
              .map((page, index, array) => (
                <React.Fragment key={page}>
                  {index > 0 && array[index - 1] !== page - 1 && (
                    <span
                      style={{
                        color: darkMode
                          ? colors.neutral.light
                          : colors.neutral.dark,
                      }}
                    >
                      ...
                    </span>
                  )}
                  <PageButton
                    darkMode={darkMode}
                    active={currentPage === page}
                    onClick={() => loadEmails(page)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {page}
                  </PageButton>
                </React.Fragment>
              ))}

            <PageButton
              darkMode={darkMode}
              disabled={currentPage >= Math.ceil(totalEmails / emailsPerPage)}
              onClick={() => loadEmails(currentPage + 1)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Next
            </PageButton>
          </PaginationControls>
        </PaginationContainer>
      )}

      {/* Email Details Modal */}
      {showEmailDetails && selectedEmail && (
        <EmailDetailsModal
          email={selectedEmail}
          darkMode={darkMode}
          onClose={() => {
            setShowEmailDetails(false);
            setSelectedEmail(null);
          }}
          onReply={replyToEmail}
        />
      )}

      {/* Thread Detail View Modal */}
      {showThreadView && selectedThread && (
        <ThreadDetailModal
          thread={selectedThread}
          darkMode={darkMode}
          onClose={() => {
            setShowThreadView(false);
            setSelectedThread(null);
          }}
          onReply={replyToEmail}
          onUpdateStatus={updateThreadStatus}
          onAssign={assignThread}
        />
      )}
    </Container>
  );
};

// Email Details Modal Component
const EmailDetailsModal = ({ email, darkMode, onClose, onReply }) => {
  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent darkMode={darkMode} onClick={(e) => e.stopPropagation()}>
        <ModalHeader darkMode={darkMode}>
          <h3>Email Details</h3>
          <CloseButton onClick={onClose} darkMode={darkMode}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          <DetailRow>
            <DetailLabel>To:</DetailLabel>
            <DetailValue darkMode={darkMode}>{email.to}</DetailValue>
          </DetailRow>

          <DetailRow>
            <DetailLabel>Subject:</DetailLabel>
            <DetailValue darkMode={darkMode}>{email.subject}</DetailValue>
          </DetailRow>

          <DetailRow>
            <DetailLabel>Status:</DetailLabel>
            <StatusBadge status={email.status}>{email.status}</StatusBadge>
          </DetailRow>

          <DetailRow>
            <DetailLabel>Timestamp:</DetailLabel>
            <DetailValue darkMode={darkMode}>
              {new Date(email.timestamp).toLocaleString()}
            </DetailValue>
          </DetailRow>

          {email.content && (
            <DetailRow>
              <DetailLabel>Content:</DetailLabel>
              <DetailContent
                darkMode={darkMode}
                dangerouslySetInnerHTML={{ __html: email.content }}
              />
            </DetailRow>
          )}

          {email.error && (
            <DetailRow>
              <DetailLabel>Error:</DetailLabel>
              <ErrorMessage>{email.error}</ErrorMessage>
            </DetailRow>
          )}
        </ModalBody>

        <div
          style={{
            padding: spacing.lg,
            borderTop: `1px solid ${
              darkMode ? colors.neutral.dark : colors.neutral.light
            }`,
            display: "flex",
            gap: spacing.sm,
            justifyContent: "flex-end",
          }}
        >
          <ActionButton
            className="view"
            onClick={() => {
              onReply(email);
              onClose();
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            📧 Reply
          </ActionButton>
        </div>
      </ModalContent>
    </ModalOverlay>
  );
};

// Thread Detail Modal Component
const ThreadDetailModal = ({
  thread,
  darkMode,
  onClose,
  onReply,
  onUpdateStatus,
  onAssign,
}) => {
  const [newMessage, setNewMessage] = useState("");
  const [showAssignMenu, setShowAssignMenu] = useState(false);

  const handleReplyToThread = () => {
    const latestEmail = thread.emails[thread.emails.length - 1];
    onReply(latestEmail);
    onClose();
  };

  const handleStatusChange = (newStatus) => {
    onUpdateStatus(thread.id, newStatus);
  };

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent darkMode={darkMode} onClick={(e) => e.stopPropagation()}>
        <ModalHeader darkMode={darkMode}>
          <div>
            <h3>Email Thread: {thread.originalSubject}</h3>
            <div
              style={{
                fontSize: "0.9rem",
                color: darkMode ? colors.neutral.light : colors.neutral.dark,
                marginTop: spacing.xs,
              }}
            >
              Conversation with {thread.customerEmail}
            </div>
          </div>
          <div
            style={{ display: "flex", gap: spacing.sm, alignItems: "center" }}
          >
            <ThreadBadge type="status" value={thread.status}>
              {thread.status}
            </ThreadBadge>
            {thread.priority !== "normal" && (
              <ThreadBadge type="priority" value={thread.priority}>
                {thread.priority}
              </ThreadBadge>
            )}
            <motion.button
              onClick={onClose}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              style={{
                background: "transparent",
                border: "none",
                color: darkMode ? colors.neutral.light : colors.neutral.dark,
                cursor: "pointer",
                fontSize: "1.2rem",
              }}
            >
              ✕
            </motion.button>
          </div>
        </ModalHeader>

        <ModalBody style={{ maxHeight: "60vh", overflowY: "auto" }}>
          {/* Thread Actions */}
          <div
            style={{
              display: "flex",
              gap: spacing.sm,
              marginBottom: spacing.lg,
              padding: spacing.md,
              background: darkMode
                ? colors.neutral.dark
                : colors.neutral.lightest,
              borderRadius: "8px",
            }}
          >
            <ThreadActionButton
              darkMode={darkMode}
              onClick={() =>
                handleStatusChange(
                  thread.status === "resolved" ? "open" : "resolved"
                )
              }
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {thread.status === "resolved" ? "🔄 Reopen" : "✅ Resolve"}
            </ThreadActionButton>
            <ThreadActionButton
              darkMode={darkMode}
              onClick={() => handleStatusChange("pending")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              ⏳ Mark Pending
            </ThreadActionButton>
            <ThreadActionButton
              darkMode={darkMode}
              onClick={handleReplyToThread}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              📧 Reply
            </ThreadActionButton>
          </div>

          {/* Email Conversation */}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: spacing.md,
            }}
          >
            {thread.emails.map((email, index) => (
              <motion.div
                key={email.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                style={{
                  background: darkMode
                    ? colors.neutral.darker
                    : colors.neutral.white,
                  border: `1px solid ${
                    darkMode ? colors.neutral.dark : colors.neutral.light
                  }`,
                  borderRadius: "8px",
                  padding: spacing.md,
                  marginLeft: index > 0 ? spacing.lg : 0, // Indent replies
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: spacing.sm,
                  }}
                >
                  <div>
                    <strong
                      style={{
                        color: darkMode
                          ? colors.neutral.white
                          : colors.neutral.darkest,
                      }}
                    >
                      {email.to}
                    </strong>
                    <div
                      style={{
                        fontSize: "0.8rem",
                        color: darkMode
                          ? colors.neutral.light
                          : colors.neutral.dark,
                      }}
                    >
                      {new Date(email.timestamp).toLocaleString()}
                    </div>
                  </div>
                  <StatusBadge status={email.status}>
                    {email.status}
                  </StatusBadge>
                </div>

                <div
                  style={{
                    fontSize: "0.9rem",
                    fontWeight: "600",
                    color: darkMode
                      ? colors.neutral.light
                      : colors.neutral.dark,
                    marginBottom: spacing.sm,
                  }}
                >
                  {email.subject}
                </div>

                {email.content && (
                  <div
                    style={{
                      color: darkMode
                        ? colors.neutral.light
                        : colors.neutral.dark,
                      lineHeight: "1.6",
                    }}
                    dangerouslySetInnerHTML={{
                      __html: email.content.replace(
                        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
                        ""
                      ),
                    }}
                  />
                )}
              </motion.div>
            ))}
          </div>
        </ModalBody>
      </ModalContent>
    </ModalOverlay>
  );
};

export default EmailManager;
