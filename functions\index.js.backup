const admin = require("firebase-admin");
const functions = require("firebase-functions");

// Initialize admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Email service will be imported when needed to avoid startup issues

// Lazy-load Stripe to avoid initialization timeout
let stripeInstance = null;

const getStripeInstance = () => {
  if (stripeInstance) return stripeInstance;

  try {
    // Try to get the key from Firebase config first
    const stripeKey =
      functions.config().stripe?.secret ||
      functions.config().stripe?.secret_key ||
      process.env.STRIPE_SECRET_KEY;

    if (!stripeKey || stripeKey === "your_stripe_secret_key_here") {
      console.warn(
        "Stripe secret key not found in config or environment variables"
      );
      throw new Error("Stripe secret key not found");
    }

    console.log(
      "Initializing Stripe with key:",
      stripeKey ? "Key found (hidden for security)" : "No key found"
    );
    stripeInstance = require("stripe")(stripeKey);
    console.log("Stripe initialized successfully");
    return stripeInstance;
  } catch (error) {
    console.error("Error initializing Stripe:", error);

    // For development/testing only - use the hardcoded key from .env file
    const hardcodedKey =
      "sk_test_51R62oRCMkLTqpmgQ8onl6m8RqRBP3BNMfvdXjBUcN9zYINn8UXeUEOIaSf1YYcrAxxVtZrzTJ7ktEYLdrvj83opZ00bGuzNYiV";
    console.log("Attempting to initialize Stripe with hardcoded key");

    try {
      stripeInstance = require("stripe")(hardcodedKey);
      console.log("Stripe initialized with hardcoded key");
      return stripeInstance;
    } catch (fallbackError) {
      console.error(
        "Failed to initialize Stripe with hardcoded key:",
        fallbackError
      );

      // Initialize with a placeholder to prevent crashes
      stripeInstance = {
        paymentIntents: { create: () => ({ client_secret: "test_secret" }) },
        customers: {
          create: () => ({}),
          list: () => ({ data: [] }),
          update: () => ({}),
        },
        paymentMethods: { list: () => ({ data: [] }) },
        refunds: { create: () => ({}) },
      };
      console.warn(
        "Using placeholder Stripe instance - payments will not work"
      );
      return stripeInstance;
    }
  }
};

const { Storage } = require("@google-cloud/storage");
const Busboy = require("busboy");
const path = require("path");
const os = require("os");
const fs = require("fs");

// Only create the storage instance when needed
let _storage = null;
const getStorage = () => {
  if (!_storage) {
    _storage = new Storage();
  }
  return _storage;
};

// Add a utility function for error handling
const handleError = (error, message) => {
  console.error(message, error);
  return { error: error.message };
};

// Add admin role function (using callable function)
exports.addAdminRole = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an admin
  if (context.auth.token.admin !== true) {
    return {
      error: "Only admins can add other admins",
    };
  }

  // Validate email
  if (!data.email || typeof data.email !== "string") {
    return {
      error: "Please provide a valid email address",
    };
  }

  try {
    // Get user by email and add custom claim
    const user = await admin.auth().getUserByEmail(data.email);
    await admin.auth().setCustomUserClaims(user.uid, {
      admin: true,
    });

    return {
      message: `Success! ${data.email} has been made an admin.`,
    };
  } catch (err) {
    console.error("Error adding admin role:", err);

    // Return specific error messages
    if (err.code === "auth/user-not-found") {
      return {
        error: `User with email ${data.email} does not exist.`,
      };
    }

    return {
      error: `Error: ${err.message}`,
    };
  }
});

// Function to initialize the first admin (for bootstrapping)
exports.initializeAdminCallable = functions.https.onCall(
  async (data, context) => {
    try {
      // Check if the user is authenticated
      if (!context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "User must be logged in"
        );
      }

      // Get the current user's email
      const adminEmail = context.auth.token.email;

      // Get user by email
      const userRecord = await admin.auth().getUserByEmail(adminEmail);

      // Set admin claim
      await admin.auth().setCustomUserClaims(userRecord.uid, { admin: true });

      console.log(`Admin role set for ${adminEmail}`);
      return {
        success: true,
        message: `Admin role set for ${adminEmail}`,
      };
    } catch (error) {
      console.error("Error initializing admin:", error);
      throw new functions.https.HttpsError("internal", error.message);
    }
  }
);

// TEMPORARY: Emergency admin setup function (NO AUTH REQUIRED)
exports.emergencyAdminSetup = functions.https.onCall(async (data, context) => {
  try {
    console.log("🚨 Emergency admin setup called");

    // Set admin for the known admin email
    const adminEmail = "<EMAIL>";

    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(adminEmail);

    // Set admin claim
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      editor: true,
    });

    console.log(`🚨 Emergency admin role set for ${adminEmail}`);
    return {
      success: true,
      message: `Emergency admin role set for ${adminEmail}`,
    };
  } catch (error) {
    console.error("🚨 Error in emergency admin setup:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});

// Define CORS options - more permissive for 2nd Gen functions
const corsOptions = {
  origin: true, // Allow requests from any origin
  methods: ["POST", "GET", "OPTIONS", "PUT", "DELETE"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
  ],
  credentials: false, // Set to false for public functions
  maxAge: 86400, // 24 hours
  optionsSuccessStatus: 200, // For legacy browser support
};

// Initialize CORS middleware with options
const cors = require("cors")(corsOptions);

// File upload function - optimize to avoid timeout
exports.uploadFile = functions.https.onRequest((req, res) => {
  // Handle preflight OPTIONS request
  if (req.method === "OPTIONS") {
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    res.set("Access-Control-Max-Age", "86400");
    res.status(204).send("");
    return;
  }

  cors(req, res, async () => {
    if (req.method !== "POST") {
      return res.status(405).json({ error: "Method Not Allowed" });
    }

    try {
      const busboy = Busboy({ headers: req.headers });
      const uploads = {};
      const fields = {};

      busboy.on("file", (fieldname, file, info) => {
        const { filename, mimeType } = info;
        console.log(`Processing file: ${filename}, mimetype: ${mimeType}`);

        const filepath = path.join(os.tmpdir(), filename);
        uploads[fieldname] = { file: file, filepath, mimeType };

        const writeStream = fs.createWriteStream(filepath);
        file.pipe(writeStream);
      });

      busboy.on("field", (fieldname, val) => {
        fields[fieldname] = val;
      });

      // Process when complete
      busboy.on("finish", async () => {
        const folder = fields.folder || "uploads";
        const file = uploads.file;

        if (!file) {
          return res.status(400).json({ error: "No file uploaded" });
        }

        const bucket = getStorage().bucket("skinglow1000.appspot.com");
        const destination = `${folder}/${Date.now()}_${path.basename(
          file.filepath
        )}`;

        await bucket.upload(file.filepath, {
          destination,
          metadata: {
            contentType: file.mimeType,
            metadata: {
              firebaseStorageDownloadTokens: admin.database().ref().push().key,
            },
          },
        });

        const fileUrl = `https://firebasestorage.googleapis.com/v0/b/skinglow1000.appspot.com/o/${encodeURIComponent(
          destination
        )}?alt=media`;

        // Clean up temp file
        fs.unlinkSync(file.filepath);

        return res.status(200).json({ success: true, fileUrl });
      });

      busboy.end(req.rawBody);
    } catch (error) {
      return res.status(500).json(handleError(error, "Error uploading file"));
    }
  });
});

// Add a simple health check function
exports.healthCheck = functions.https.onRequest((req, res) => {
  // Set CORS headers
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  res.status(200).send("Firebase Functions are running");
});

// Test contact form data function
exports.testContactFormData = functions.https.onCall(async (data, context) => {
  console.log("=== TEST CONTACT FORM DATA ===");
  console.log("Raw data:", data);
  console.log("Data type:", typeof data);
  console.log("Data keys:", Object.keys(data || {}));
  console.log("Data stringified:", JSON.stringify(data, null, 2));

  const { name, email, subject, message } = data;
  console.log("Extracted fields:", { name, email, subject, message });

  return {
    success: true,
    receivedData: data,
    extractedFields: { name, email, subject, message },
    emailCheck: {
      exists: !!email,
      type: typeof email,
      length: email?.length,
      value: email,
    },
  };
});

// Test payment function with simpler configuration
exports.testPayment = functions.https.onRequest((req, res) => {
  // Set CORS headers
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method === "POST") {
    res.status(200).json({
      message: "Test payment endpoint working",
      body: req.body,
      method: req.method,
    });
  } else {
    res.status(200).json({
      message: "Test payment endpoint - use POST method",
      method: req.method,
    });
  }
});

// Create a payment intent with Stripe
exports.createPaymentIntent = functions.https.onRequest((req, res) => {
  // Set CORS headers for all requests
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE");
  res.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, X-Requested-With, Accept, Origin"
  );
  res.set("Access-Control-Max-Age", "86400");

  // Handle preflight OPTIONS request
  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  cors(req, res, async () => {
    try {
      if (req.method !== "POST") {
        return res.status(405).json({ error: "Method Not Allowed" });
      }

      const {
        amount,
        currency = "usd",
        customer_email,
        payment_method_id,
        save_payment_method,
      } = req.body;

      if (!amount || amount <= 0) {
        return res.status(400).json({ error: "Valid amount is required" });
      }

      // Get Stripe instance
      const stripe = getStripeInstance();

      // Create or retrieve a customer
      let customer;
      if (customer_email) {
        // Check if customer already exists
        const customers = await stripe.customers.list({
          email: customer_email,
          limit: 1,
        });

        if (customers.data.length > 0) {
          customer = customers.data[0];
        } else {
          // Create a new customer
          customer = await stripe.customers.create({
            email: customer_email,
          });
        }
      }

      // Create payment intent options
      const paymentIntentOptions = {
        amount: Math.round(amount), // Stripe requires integer amount in cents
        currency: currency,
        customer: customer ? customer.id : undefined,
        metadata: {
          customer_email: customer_email,
        },
      };

      // If using a saved payment method
      if (payment_method_id) {
        paymentIntentOptions.payment_method = payment_method_id;
        paymentIntentOptions.confirm = true;
        paymentIntentOptions.setup_future_usage = save_payment_method
          ? "off_session"
          : undefined;
      } else {
        // For new payment methods
        paymentIntentOptions.automatic_payment_methods = {
          enabled: true,
        };

        if (save_payment_method && customer) {
          paymentIntentOptions.setup_future_usage = "off_session";
        }
      }

      // Create a payment intent
      const paymentIntent = await stripe.paymentIntents.create(
        paymentIntentOptions
      );

      // Return the client secret
      return res.status(200).json({
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        customerId: customer ? customer.id : null,
      });
    } catch (error) {
      console.error("Error creating payment intent:", error);
      return res
        .status(500)
        .json(handleError(error, "Error creating payment intent"));
    }
  });
});

// Get customer payment methods
exports.getCustomerPaymentMethods = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      if (req.method !== "GET") {
        return res.status(405).json({ error: "Method Not Allowed" });
      }

      const { customer_email } = req.query;

      if (!customer_email) {
        return res.status(400).json({ error: "Customer email is required" });
      }

      // Get Stripe instance
      const stripe = getStripeInstance();

      // Find customer by email
      const customers = await stripe.customers.list({
        email: customer_email,
        limit: 1,
      });

      if (customers.data.length === 0) {
        return res.status(404).json({ error: "Customer not found" });
      }

      const customer = customers.data[0];

      // Get payment methods
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customer.id,
        type: "card",
      });

      // Format payment methods for client
      const formattedPaymentMethods = paymentMethods.data.map((pm) => ({
        id: pm.id,
        brand: pm.card.brand,
        last4: pm.card.last4,
        expMonth: pm.card.exp_month,
        expYear: pm.card.exp_year,
        isDefault: pm.id === customer.invoice_settings?.default_payment_method,
      }));

      return res.status(200).json({ paymentMethods: formattedPaymentMethods });
    } catch (error) {
      console.error("Error getting payment methods:", error);
      return res
        .status(500)
        .json(handleError(error, "Error getting payment methods"));
    }
  });
});

// Set default payment method
exports.setDefaultPaymentMethod = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      if (req.method !== "POST") {
        return res.status(405).json({ error: "Method Not Allowed" });
      }

      const { customer_email, payment_method_id } = req.body;

      if (!customer_email || !payment_method_id) {
        return res
          .status(400)
          .json({ error: "Customer email and payment method ID are required" });
      }

      // Get Stripe instance
      const stripe = getStripeInstance();

      // Find customer by email
      const customers = await stripe.customers.list({
        email: customer_email,
        limit: 1,
      });

      if (customers.data.length === 0) {
        return res.status(404).json({ error: "Customer not found" });
      }

      const customer = customers.data[0];

      // Update customer's default payment method
      await stripe.customers.update(customer.id, {
        invoice_settings: {
          default_payment_method: payment_method_id,
        },
      });

      return res.status(200).json({ success: true });
    } catch (error) {
      console.error("Error setting default payment method:", error);
      return res
        .status(500)
        .json(handleError(error, "Error setting default payment method"));
    }
  });
});

// Update order status
exports.updateOrderStatus = functions.https.onCall(async (data, context) => {
  try {
    // Debug logging
    console.log("updateOrderStatus called with context:", {
      auth: context.auth
        ? {
            uid: context.auth.uid,
            token: context.auth.token
              ? {
                  admin: context.auth.token.admin,
                  editor: context.auth.token.editor,
                  email: context.auth.token.email,
                }
              : null,
          }
        : null,
      data: data,
    });

    // Check if the request is made by an admin or editor
    if (!context.auth) {
      console.error("No auth context found");
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to perform this action"
      );
    }

    if (!context.auth.token) {
      console.error("No token found in auth context");
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication token not found"
      );
    }

    if (!context.auth.token.admin && !context.auth.token.editor) {
      console.error("User lacks admin/editor permissions:", {
        admin: context.auth.token.admin,
        editor: context.auth.token.editor,
      });
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins or editors can update order status"
      );
    }

    const { orderId, status, trackingNumber } = data;

    if (!orderId || !status) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID and status are required"
      );
    }

    // Valid order statuses
    const validStatuses = [
      "processing",
      "shipped",
      "delivered",
      "cancelled",
      "refunded",
    ];

    if (!validStatuses.includes(status)) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        `Status must be one of: ${validStatuses.join(", ")}`
      );
    }

    // Update the order in Firestore
    const orderRef = admin.firestore().collection("orders").doc(orderId);
    const orderDoc = await orderRef.get();

    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found");
    }

    const updateData = {
      status: status,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    // Add tracking number if provided
    if (trackingNumber) {
      updateData.trackingNumber = trackingNumber;
    }

    await orderRef.update(updateData);

    // If order is cancelled or refunded and there's a Stripe payment, handle refund
    const orderData = orderDoc.data();
    if (
      (status === "cancelled" || status === "refunded") &&
      orderData.payment &&
      orderData.payment.method === "stripe" &&
      orderData.payment.transactionId
    ) {
      try {
        // Get Stripe instance and create a refund
        const stripe = getStripeInstance();
        await stripe.refunds.create({
          payment_intent: orderData.payment.transactionId,
          reason:
            status === "cancelled" ? "requested_by_customer" : "duplicate",
        });
      } catch (refundError) {
        console.error("Error processing refund:", refundError);
        // Continue with order status update even if refund fails
      }
    }

    // Send status update email to customer
    try {
      const { sendEmail, emailTemplates } = require("./services/emailService");

      const emailTemplate = emailTemplates.orderStatusUpdate({
        orderId: orderId,
        status: status,
        customerName: orderData.shipping?.name || "Valued Customer",
        items: orderData.items || [],
        trackingNumber: orderData.trackingNumber || null,
      });

      await sendEmail({
        to: orderData.userEmail,
        subject: emailTemplate.subject,
        content: emailTemplate.content,
        type: emailTemplate.type,
        orderReference: orderId,
      });

      console.log(
        `Status update email sent for order ${orderId} - status: ${status}`
      );
    } catch (emailError) {
      console.error("Error sending status update email:", emailError);
      // Don't fail the status update if email fails
    }

    // Schedule review request email if order is delivered
    if (status === "delivered") {
      try {
        const reviewRequestTime = new Date(
          Date.now() + 7 * 24 * 60 * 60 * 1000
        ); // 7 days from now

        await admin
          .firestore()
          .collection("scheduledEmails")
          .add({
            type: "reviewRequest",
            orderId: orderId,
            userEmail: orderData.userEmail,
            scheduledFor: admin.firestore.Timestamp.fromDate(reviewRequestTime),
            processed: false,
          });

        console.log(`Review request email scheduled for order ${orderId}`);
      } catch (scheduleError) {
        console.error("Error scheduling review request email:", scheduleError);
        // Don't fail the status update if scheduling fails
      }
    }

    return { success: true, message: `Order status updated to ${status}` };
  } catch (error) {
    console.error("Error updating order status:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Get user orders
exports.getUserOrders = functions.https.onCall(async (data, context) => {
  try {
    // Check if the user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to view your orders"
      );
    }

    const userId = context.auth.uid;

    // Query orders for this user
    const ordersRef = admin.firestore().collection("orders");
    let query = ordersRef.where("userId", "==", userId);

    // Add sorting if provided
    if (data.sortBy) {
      const sortField = data.sortBy === "date" ? "createdAt" : "total";
      const sortDirection = data.sortDirection === "asc" ? "asc" : "desc";
      query = query.orderBy(sortField, sortDirection);
    } else {
      // Default sort by date descending
      query = query.orderBy("createdAt", "desc");
    }

    const snapshot = await query.get();

    // Format orders for response
    const orders = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt
          ? data.createdAt.toDate().toISOString()
          : null,
        updatedAt: data.updatedAt
          ? data.updatedAt.toDate().toISOString()
          : null,
      };
    });

    return { orders };
  } catch (error) {
    console.error("Error getting user orders:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Cancel order (for users)
exports.cancelOrder = functions.https.onCall(async (data, context) => {
  try {
    // Check if the user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to cancel an order"
      );
    }

    const userId = context.auth.uid;
    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID is required"
      );
    }

    // Get the order
    const orderRef = admin.firestore().collection("orders").doc(orderId);
    const orderDoc = await orderRef.get();

    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found");
    }

    const orderData = orderDoc.data();

    // Check if this order belongs to the user
    if (orderData.userId !== userId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only cancel your own orders"
      );
    }

    // Check if the order can be cancelled
    if (
      orderData.status === "delivered" ||
      orderData.status === "cancelled" ||
      orderData.status === "refunded"
    ) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        `Cannot cancel order with status: ${orderData.status}`
      );
    }

    // Update order status
    await orderRef.update({
      status: "cancelled",
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // If there's a Stripe payment, handle refund
    if (
      orderData.payment &&
      orderData.payment.method === "stripe" &&
      orderData.payment.transactionId
    ) {
      try {
        // Get Stripe instance and create a refund
        const stripe = getStripeInstance();
        await stripe.refunds.create({
          payment_intent: orderData.payment.transactionId,
          reason: "requested_by_customer",
        });
      } catch (refundError) {
        console.error("Error processing refund:", refundError);
        // Continue with order cancellation even if refund fails
      }
    }

    return { success: true, message: "Order cancelled successfully" };
  } catch (error) {
    console.error("Error cancelling order:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Get all orders (for admin)
exports.getAllOrders = functions.https.onCall(async (data, context) => {
  try {
    // Check if the request is made by an admin or editor
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to perform this action"
      );
    }

    if (!context.auth.token.admin && !context.auth.token.editor) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins or editors can view all orders"
      );
    }

    // Parse pagination parameters
    const limit = data.limit || 20;
    const page = data.page || 1;
    const startAfter = data.startAfter || null;

    // Parse filter parameters
    const filters = data.filters || {};
    const { status, dateRange, minTotal, maxTotal, search } = filters;

    // Build query
    const ordersRef = admin.firestore().collection("orders");
    let query = ordersRef;

    // Apply filters
    if (status && status !== "all") {
      query = query.where("status", "==", status);
    }

    if (dateRange && dateRange.start) {
      const startDate = new Date(dateRange.start);
      query = query.where("createdAt", ">=", startDate);
    }

    if (dateRange && dateRange.end) {
      const endDate = new Date(dateRange.end);
      query = query.where("createdAt", "<=", endDate);
    }

    // Add sorting
    const sortField = data.sortBy === "total" ? "total" : "createdAt";
    const sortDirection = data.sortDirection === "asc" ? "asc" : "desc";
    query = query.orderBy(sortField, sortDirection);

    // Apply pagination
    if (startAfter) {
      const startAfterDoc = await admin
        .firestore()
        .collection("orders")
        .doc(startAfter)
        .get();
      if (startAfterDoc.exists) {
        query = query.startAfter(startAfterDoc);
      }
    } else if (page > 1) {
      // Skip pages if startAfter is not provided
      query = query.limit((page - 1) * limit);
      const skipSnapshot = await query.get();
      const lastVisible = skipSnapshot.docs[skipSnapshot.docs.length - 1];
      if (lastVisible) {
        query = ordersRef
          .orderBy(sortField, sortDirection)
          .startAfter(lastVisible);
      } else {
        // If we can't skip ahead, return empty results
        return { orders: [], hasMore: false, total: 0 };
      }
    }

    // Apply limit
    query = query.limit(limit);

    // Execute query
    const snapshot = await query.get();

    // Get total count (for pagination)
    const countSnapshot = await ordersRef.count().get();
    const total = countSnapshot.data().count;

    // Format orders for response
    let orders = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt
          ? data.createdAt.toDate().toISOString()
          : null,
        updatedAt: data.updatedAt
          ? data.updatedAt.toDate().toISOString()
          : null,
      };
    });

    // Apply client-side filters that can't be done in Firestore query
    if (minTotal !== undefined) {
      orders = orders.filter((order) => order.total >= minTotal);
    }

    if (maxTotal !== undefined) {
      orders = orders.filter((order) => order.total <= maxTotal);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      orders = orders.filter(
        (order) =>
          (order.id && order.id.toLowerCase().includes(searchLower)) ||
          (order.shipping &&
            order.shipping.name &&
            order.shipping.name.toLowerCase().includes(searchLower)) ||
          (order.userEmail &&
            order.userEmail.toLowerCase().includes(searchLower))
      );
    }

    // Check if there are more results
    const hasMore = snapshot.docs.length === limit;

    return {
      orders,
      hasMore,
      total,
      lastVisible:
        snapshot.docs.length > 0
          ? snapshot.docs[snapshot.docs.length - 1].id
          : null,
    };
  } catch (error) {
    console.error("Error getting all orders:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Get admin users function
exports.getAdminUsers = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an admin
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "You must be logged in to perform this action"
    );
  }

  if (context.auth.token.admin !== true) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Only admins can view the admin list"
    );
  }

  // Log for debugging
  console.log("User requesting admin list:", context.auth.uid);
  console.log("User token:", context.auth.token);

  try {
    // List all users with admin claim
    const listUsersResult = await admin.auth().listUsers();
    const admins = [];

    listUsersResult.users.forEach((userRecord) => {
      const claims = userRecord.customClaims || {};
      if (claims.admin) {
        admins.push({
          email: userRecord.email,
          uid: userRecord.uid,
          displayName: userRecord.displayName,
        });
      }
    });

    return { admins };
  } catch (error) {
    console.error("Error fetching admin users:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Remove admin role
exports.removeAdminRole = functions.https.onCall(async (data, context) => {
  // Check if request is made by an admin
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "You must be logged in to perform this action"
    );
  }

  if (!context.auth.token.admin) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Only administrators can remove admin privileges"
    );
  }

  // Get the email from the request
  const { email } = data;

  if (!email) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Email is required"
    );
  }

  try {
    // Get the user by email
    const user = await admin.auth().getUserByEmail(email);

    // Check if trying to remove own admin privileges
    if (user.email === context.auth.token.email) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "You cannot remove your own admin privileges"
      );
    }

    // Remove admin claim by setting it to false
    const customClaims = user.customClaims || {};
    customClaims.admin = false;

    await admin.auth().setCustomUserClaims(user.uid, customClaims);

    return {
      message: `Successfully removed admin privileges from ${email}`,
    };
  } catch (error) {
    console.error("Error removing admin role:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Add editor role function
exports.addEditorRole = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an admin
  if (context.auth.token.admin !== true) {
    return {
      error: "Only admins can add editors",
    };
  }

  // Validate email
  if (!data.email || typeof data.email !== "string") {
    return {
      error: "Please provide a valid email address",
    };
  }

  try {
    // Get user by email and add custom claim
    const user = await admin.auth().getUserByEmail(data.email);

    // Get existing claims
    const customClaims = user.customClaims || {};

    // Add editor role
    customClaims.editor = true;

    await admin.auth().setCustomUserClaims(user.uid, customClaims);

    return {
      message: `Success! ${data.email} has been made an editor.`,
    };
  } catch (err) {
    console.error("Error adding editor role:", err);

    // Return specific error messages
    if (err.code === "auth/user-not-found") {
      return {
        error: `User with email ${data.email} does not exist.`,
      };
    }

    return {
      error: `Error: ${err.message}`,
    };
  }
});

// Remove editor role function
exports.removeEditorRole = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an admin
  if (context.auth.token.admin !== true) {
    return {
      error: "Only admins can remove editors",
    };
  }

  // Validate email
  if (!data.email || typeof data.email !== "string") {
    return {
      error: "Please provide a valid email address",
    };
  }

  try {
    // Get user by email and remove custom claim
    const user = await admin.auth().getUserByEmail(data.email);

    // Get existing claims
    const customClaims = user.customClaims || {};

    // Remove editor role
    delete customClaims.editor;

    await admin.auth().setCustomUserClaims(user.uid, customClaims);

    return {
      message: `Success! ${data.email} has had editor privileges removed.`,
    };
  } catch (err) {
    console.error("Error removing editor role:", err);

    // Return specific error messages
    if (err.code === "auth/user-not-found") {
      return {
        error: `User with email ${data.email} does not exist.`,
      };
    }

    return {
      error: `Error: ${err.message}`,
    };
  }
});

// ============================================================================
// EMAIL FUNCTIONS
// ============================================================================

// Simple test function without dependencies
exports.simpleEmailTest = functions.https.onCall(async (data, context) => {
  try {
    console.log("Simple test function called with data:", data);
    console.log("Data type:", typeof data);
    console.log("Data keys:", Object.keys(data || {}));

    if (!data) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "No data received"
      );
    }

    // Extract the actual email data from the nested structure
    const emailData = data.data || data;
    console.log("Email data extracted:", emailData);

    const { to, subject, message } = emailData;
    console.log("Extracted fields:", { to, subject, message });

    if (!to || !subject || !message) {
      console.error("Missing required fields:", { to, subject, message });
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Email address, subject, and message are required"
      );
    }

    // NOW USING SENDGRID - Send actual emails!
    try {
      const { sendEmail } = require("./services/emailService");

      // Create email content
      const emailContent = `
        <h2>${subject}</h2>
        <div style="white-space: pre-wrap; line-height: 1.6; margin-top: 20px;">${message}</div>
      `;

      // Send email via SendGrid
      const result = await sendEmail({
        to,
        subject: subject,
        content: emailContent,
        type: "general",
      });

      console.log("Email sent successfully via SendGrid!");

      return {
        success: true,
        message: `✅ Email sent successfully via SendGrid! Check your inbox at ${to}`,
        status: "sent_via_sendgrid",
        result,
      };
    } catch (error) {
      console.error("Error sending email via SendGrid:", error);
      return {
        success: false,
        message: `Error sending email: ${error.message}`,
        status: "sendgrid_error",
        error: error.message,
      };
    }
  } catch (error) {
    console.error("Error in simple test:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

const { sendEmail, emailTemplates } = require("./services/emailService");

// Production email testing function (admin auth required)
exports.testEmailSystem = functions.https.onCall(async (data, context) => {
  try {
    // Require authentication
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    // Require admin or editor privileges
    if (!context.auth.token.admin && !context.auth.token.editor) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "User must have admin or editor privileges"
      );
    }

    console.log("Email test function called by:", context.auth.token.email);

    // Validate data exists
    if (!data) {
      console.error("No data received");
      throw new functions.https.HttpsError(
        "invalid-argument",
        "No data received"
      );
    }

    // Extract the actual email data from the nested structure (same fix as simpleEmailTest)
    const emailData = data.data || data;
    console.log("Email data extracted:", emailData);

    const { to, subject, message } = emailData;
    console.log("Extracted fields:", { to, subject, message });

    if (!to || !subject || !message) {
      console.error("Missing required fields:", { to, subject, message });
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Email address, subject, and message are required"
      );
    }

    // Create professional email content
    const emailContent = `
      <h2>${subject}</h2>
      <div style="white-space: pre-wrap; line-height: 1.6; margin-top: 20px;">${message}</div>
    `;

    // Send email with error handling for circular references
    let result;
    try {
      result = await sendEmail({
        to,
        subject: subject,
        content: emailContent,
        type: "general",
      });
      console.log("sendEmail completed successfully");
    } catch (emailError) {
      console.error("Error in sendEmail:", emailError.message);
      // Return a safe response without circular references
      return {
        success: false,
        message: `Email sending failed: ${emailError.message}`,
        status: "error",
      };
    }

    console.log("Email processed:", {
      to,
      subject,
      status: result?.status || "unknown",
    });

    // Create a safe response object without circular references
    const safeResponse = {
      success: true,
      message:
        result?.status === "sent"
          ? `Email sent successfully to ${to}!`
          : result?.status === "logged_only"
          ? "Email logged (SendGrid not configured)"
          : "Email processing completed",
      status: result?.status || "unknown",
    };

    return safeResponse;
  } catch (error) {
    console.error("Error processing email:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Send custom email (Admin function)
exports.sendCustomEmail = functions.https.onCall(async (data, context) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!context.auth) {
      console.log("No authentication context found for sendCustomEmail");
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    console.log("User auth token for sendCustomEmail:", context.auth.token);

    if (!context.auth.token.admin && !context.auth.token.editor) {
      console.log("User is not admin or editor for sendCustomEmail");
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins or editors can send custom emails"
      );
    }

    const { to, subject, message, orderReference } = data;

    if (!to || !subject || !message) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Email address, subject, and message are required"
      );
    }

    // Create email content
    let emailContent = `
      <h2>${subject}</h2>
      <div style="white-space: pre-wrap; line-height: 1.6;">${message}</div>
    `;

    if (orderReference) {
      emailContent = `
        <h2>${subject}</h2>
        <p><strong>Regarding Order:</strong> ${orderReference}</p>
        <div style="white-space: pre-wrap; line-height: 1.6; margin-top: 20px;">${message}</div>
      `;
    }

    // Send email using the email service
    const result = await sendEmail({
      to,
      subject,
      content: emailContent,
      type: "general",
      orderReference,
    });

    console.log("Email processed:", { to, subject, status: result.status });

    return {
      success: true,
      message:
        result.status === "sent"
          ? "Email sent successfully!"
          : result.status === "logged_only"
          ? "Email logged (SendGrid not configured)"
          : "Email processing failed",
      status: result.status,
    };
  } catch (error) {
    console.error("Error processing email:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// TODO: Add Firestore triggers after resolving Firebase Functions v6 compatibility
// For now, we'll use callable functions to trigger emails manually

// Send order confirmation email (callable function)
exports.sendOrderConfirmationEmail = functions.https.onCall(
  async (data, context) => {
    try {
      // Order confirmation emails can be sent without authentication
      // since they're triggered by successful payments from checkout

      const { orderId, userEmail, items, total, shipping } = data;

      if (!orderId || !userEmail) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Order ID and user email are required"
        );
      }

      // Generate order confirmation email
      const emailTemplate = emailTemplates.orderConfirmation({
        orderId,
        items: items || [],
        total: total || 0,
        shipping: shipping || {},
      });

      // Send confirmation email
      const result = await sendEmail({
        to: userEmail,
        subject: emailTemplate.subject,
        content: emailTemplate.content,
        type: emailTemplate.type,
        orderReference: orderId,
      });

      console.log("Order confirmation email sent for order:", orderId);

      return {
        success: true,
        message:
          result.status === "sent"
            ? "Order confirmation email sent successfully!"
            : "Order confirmation email logged",
        status: result.status,
      };
    } catch (error) {
      console.error("Error sending order confirmation email:", error);
      throw new functions.https.HttpsError("internal", error.message);
    }
  }
);

// Abandoned Cart Recovery System
exports.trackAbandonedCart = functions.https.onCall(async (data, context) => {
  try {
    const { cartItems, userEmail, userId } = data;

    if (!cartItems || cartItems.length === 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Cart items are required"
      );
    }

    // Create abandoned cart record
    const abandonedCartData = {
      userId: userId || null,
      userEmail: userEmail || null,
      cartItems: cartItems,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      emailSent: false,
      recovered: false,
    };

    const cartRef = await admin
      .firestore()
      .collection("abandonedCarts")
      .add(abandonedCartData);

    // Schedule recovery email for 1 hour later
    const scheduleTime = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    await admin
      .firestore()
      .collection("scheduledEmails")
      .add({
        type: "abandonedCart",
        cartId: cartRef.id,
        userEmail: userEmail,
        scheduledFor: admin.firestore.Timestamp.fromDate(scheduleTime),
        processed: false,
      });

    console.log(`Abandoned cart tracked for ${userEmail || "guest user"}`);
    return { success: true, cartId: cartRef.id };
  } catch (error) {
    console.error("Error tracking abandoned cart:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Process scheduled abandoned cart emails
// Temporarily disabled - will fix scheduled functions later
// exports.processAbandonedCartEmails = functions.pubsub
//   .schedule("every 30 minutes")
//   .onRun(async (context) => {
    try {
      const now = admin.firestore.Timestamp.now();

      // Get scheduled emails that are due
      const scheduledEmailsRef = admin
        .firestore()
        .collection("scheduledEmails");
      const dueEmails = await scheduledEmailsRef
        .where("type", "==", "abandonedCart")
        .where("processed", "==", false)
        .where("scheduledFor", "<=", now)
        .get();

      console.log(`Processing ${dueEmails.size} abandoned cart emails`);

      for (const emailDoc of dueEmails.docs) {
        const emailData = emailDoc.data();

        try {
          // Get the abandoned cart data
          const cartDoc = await admin
            .firestore()
            .collection("abandonedCarts")
            .doc(emailData.cartId)
            .get();

          if (!cartDoc.exists) {
            console.log(`Cart ${emailData.cartId} not found, skipping`);
            continue;
          }

          const cartData = cartDoc.data();

          // Check if cart was recovered (user completed purchase)
          if (cartData.recovered) {
            console.log(
              `Cart ${emailData.cartId} was recovered, skipping email`
            );
            await emailDoc.ref.update({ processed: true });
            continue;
          }

          // Send abandoned cart recovery email
          const {
            sendEmail,
            emailTemplates,
          } = require("./services/emailService");

          const emailTemplate = emailTemplates.abandonedCartRecovery({
            cartItems: cartData.cartItems,
            userEmail: cartData.userEmail,
          });

          await sendEmail({
            to: cartData.userEmail,
            subject: emailTemplate.subject,
            content: emailTemplate.content,
            type: emailTemplate.type,
          });

          // Mark email as sent
          await emailDoc.ref.update({ processed: true });
          await cartDoc.ref.update({ emailSent: true });

          console.log(`Abandoned cart email sent to ${cartData.userEmail}`);
        } catch (emailError) {
          console.error(`Error sending abandoned cart email:`, emailError);
          // Mark as processed to avoid retry loops
          await emailDoc.ref.update({ processed: true });
        }
      }

      return null;
    } catch (error) {
      console.error("Error processing abandoned cart emails:", error);
      return null;
    }
  });

// Mark cart as recovered when order is completed
exports.markCartRecovered = functions.https.onCall(async (data, context) => {
  try {
    const { userEmail, userId } = data;

    if (!userEmail && !userId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "User email or ID is required"
      );
    }

    // Find and mark recent abandoned carts as recovered
    const abandonedCartsRef = admin.firestore().collection("abandonedCarts");
    let query = abandonedCartsRef.where("recovered", "==", false);

    if (userEmail) {
      query = query.where("userEmail", "==", userEmail);
    } else {
      query = query.where("userId", "==", userId);
    }

    const recentCarts = await query
      .where(
        "createdAt",
        ">",
        admin.firestore.Timestamp.fromDate(
          new Date(Date.now() - 24 * 60 * 60 * 1000)
        )
      ) // Last 24 hours
      .get();

    const batch = admin.firestore().batch();
    recentCarts.docs.forEach((doc) => {
      batch.update(doc.ref, { recovered: true });
    });

    await batch.commit();
    console.log(
      `Marked ${recentCarts.size} carts as recovered for ${userEmail || userId}`
    );

    return { success: true, cartsRecovered: recentCarts.size };
  } catch (error) {
    console.error("Error marking cart as recovered:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Process scheduled review request emails
// exports.processReviewRequestEmails = functions.pubsub
  .schedule("every 6 hours")
  .onRun(async (context) => {
    try {
      const now = admin.firestore.Timestamp.now();

      // Get scheduled review request emails that are due
      const scheduledEmailsRef = admin
        .firestore()
        .collection("scheduledEmails");
      const dueEmails = await scheduledEmailsRef
        .where("type", "==", "reviewRequest")
        .where("processed", "==", false)
        .where("scheduledFor", "<=", now)
        .get();

      console.log(`Processing ${dueEmails.size} review request emails`);

      for (const emailDoc of dueEmails.docs) {
        const emailData = emailDoc.data();

        try {
          // Get the order data
          const orderDoc = await admin
            .firestore()
            .collection("orders")
            .doc(emailData.orderId)
            .get();

          if (!orderDoc.exists) {
            console.log(`Order ${emailData.orderId} not found, skipping`);
            await emailDoc.ref.update({ processed: true });
            continue;
          }

          const orderData = orderDoc.data();

          // Only send if order is still delivered (not cancelled/refunded)
          if (orderData.status !== "delivered") {
            console.log(
              `Order ${emailData.orderId} status changed to ${orderData.status}, skipping review request`
            );
            await emailDoc.ref.update({ processed: true });
            continue;
          }

          // Send review request email
          const {
            sendEmail,
            emailTemplates,
          } = require("./services/emailService");

          const emailTemplate = emailTemplates.reviewRequest({
            orderId: emailData.orderId,
            customerName: orderData.shipping?.name || "Valued Customer",
            items: orderData.items || [],
            userEmail: orderData.userEmail,
          });

          await sendEmail({
            to: orderData.userEmail,
            subject: emailTemplate.subject,
            content: emailTemplate.content,
            type: emailTemplate.type,
            orderReference: emailData.orderId,
          });

          // Mark email as sent
          await emailDoc.ref.update({ processed: true });

          console.log(
            `Review request email sent for order ${emailData.orderId}`
          );
        } catch (emailError) {
          console.error(`Error sending review request email:`, emailError);
          // Mark as processed to avoid retry loops
          await emailDoc.ref.update({ processed: true });
        }
      }

      return null;
    } catch (error) {
      console.error("Error processing review request emails:", error);
      return null;
    }
  });

// Inventory Alert System
// exports.checkLowStock = functions.pubsub
  .schedule("every 24 hours")
  .onRun(async (context) => {
    try {
      console.log("Checking for low stock products...");

      // Get all products
      const productsRef = admin.firestore().collection("products");
      const productsSnapshot = await productsRef.get();

      const lowStockProducts = [];

      productsSnapshot.docs.forEach((doc) => {
        const product = doc.data();
        const stockLevel = product.stock || 0;
        const lowStockThreshold = product.lowStockThreshold || 10;

        if (stockLevel <= lowStockThreshold && stockLevel > 0) {
          lowStockProducts.push({
            id: doc.id,
            name: product.name,
            stock: stockLevel,
            threshold: lowStockThreshold,
          });
        }
      });

      if (lowStockProducts.length > 0) {
        console.log(`Found ${lowStockProducts.length} low stock products`);

        // Send admin notification
        const {
          sendEmail,
          emailTemplates,
        } = require("./services/emailService");

        const emailTemplate = emailTemplates.lowStockAlert({
          products: lowStockProducts,
        });

        // Send to admin email
        await sendEmail({
          to: "<EMAIL>", // Replace with actual admin email
          subject: emailTemplate.subject,
          content: emailTemplate.content,
          type: emailTemplate.type,
        });

        console.log("Low stock alert email sent to admin");
      }

      return null;
    } catch (error) {
      console.error("Error checking low stock:", error);
      return null;
    }
  });

// Subscribe to restock notifications
exports.subscribeToRestock = functions.https.onCall(async (data, context) => {
  try {
    const { productId, userEmail } = data;

    if (!productId || !userEmail) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Product ID and email are required"
      );
    }

    // Check if product exists
    const productDoc = await admin
      .firestore()
      .collection("products")
      .doc(productId)
      .get();

    if (!productDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Product not found");
    }

    // Add to restock notifications
    const notificationData = {
      productId: productId,
      userEmail: userEmail,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      notified: false,
    };

    await admin
      .firestore()
      .collection("restockNotifications")
      .add(notificationData);

    console.log(
      `Restock notification added for ${userEmail} on product ${productId}`
    );

    return {
      success: true,
      message: "You'll be notified when this product is back in stock!",
    };
  } catch (error) {
    console.error("Error subscribing to restock:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Trigger restock notifications when product stock is updated
exports.onProductUpdate = functions.firestore
  .document("products/{productId}")
  .onUpdate(async (change, context) => {
    try {
      const before = change.before.data();
      const after = change.after.data();
      const productId = context.params.productId;

      // Check if stock increased from 0 or low stock to available
      const wasOutOfStock = (before.stock || 0) === 0;
      const isNowInStock = (after.stock || 0) > 0;

      if (wasOutOfStock && isNowInStock) {
        console.log(
          `Product ${productId} is back in stock, sending notifications...`
        );

        // Get all users waiting for restock notifications
        const notificationsRef = admin
          .firestore()
          .collection("restockNotifications");
        const pendingNotifications = await notificationsRef
          .where("productId", "==", productId)
          .where("notified", "==", false)
          .get();

        if (pendingNotifications.empty) {
          console.log("No pending restock notifications found");
          return null;
        }

        const {
          sendEmail,
          emailTemplates,
        } = require("./services/emailService");

        // Send notifications to all waiting users
        const batch = admin.firestore().batch();

        for (const notificationDoc of pendingNotifications.docs) {
          const notification = notificationDoc.data();

          try {
            const emailTemplate = emailTemplates.restockNotification({
              productId: productId,
              productName: after.name,
              productImage: after.images?.[0] || null,
              userEmail: notification.userEmail,
            });

            await sendEmail({
              to: notification.userEmail,
              subject: emailTemplate.subject,
              content: emailTemplate.content,
              type: emailTemplate.type,
            });

            // Mark notification as sent
            batch.update(notificationDoc.ref, { notified: true });

            console.log(
              `Restock notification sent to ${notification.userEmail}`
            );
          } catch (emailError) {
            console.error(
              `Error sending restock notification to ${notification.userEmail}:`,
              emailError
            );
          }
        }

        await batch.commit();
        console.log(`Sent ${pendingNotifications.size} restock notifications`);
      }

      return null;
    } catch (error) {
      console.error("Error in product update trigger:", error);
      return null;
    }
  });

// Customer Loyalty Program System
exports.updateLoyaltyPoints = functions.https.onCall(async (data, context) => {
  try {
    const { userId, points, reason, orderId } = data;

    if (!userId || points === undefined) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "User ID and points are required"
      );
    }

    // Get or create loyalty profile
    const loyaltyRef = admin
      .firestore()
      .collection("loyaltyProgram")
      .doc(userId);
    const loyaltyDoc = await loyaltyRef.get();

    let currentPoints = 0;
    let currentTier = "Bronze";
    let totalEarned = 0;

    if (loyaltyDoc.exists) {
      const data = loyaltyDoc.data();
      currentPoints = data.points || 0;
      currentTier = data.tier || "Bronze";
      totalEarned = data.totalEarned || 0;
    }

    const newPoints = Math.max(0, currentPoints + points);
    const newTotalEarned = totalEarned + Math.max(0, points);

    // Determine tier based on total earned points
    let newTier = "Bronze";
    if (newTotalEarned >= 5000) newTier = "Platinum";
    else if (newTotalEarned >= 2000) newTier = "Gold";
    else if (newTotalEarned >= 500) newTier = "Silver";

    // Update loyalty profile
    await loyaltyRef.set(
      {
        userId: userId,
        points: newPoints,
        tier: newTier,
        totalEarned: newTotalEarned,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      },
      { merge: true }
    );

    // Log the transaction
    await admin
      .firestore()
      .collection("loyaltyTransactions")
      .add({
        userId: userId,
        points: points,
        reason: reason || "Points adjustment",
        orderId: orderId || null,
        balanceBefore: currentPoints,
        balanceAfter: newPoints,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      });

    // Check for tier upgrade and send notification
    if (newTier !== currentTier) {
      try {
        // Get user email
        const userDoc = await admin.auth().getUser(userId);

        const {
          sendEmail,
          emailTemplates,
        } = require("./services/emailService");

        const emailTemplate = emailTemplates.tierUpgrade({
          userEmail: userDoc.email,
          newTier: newTier,
          previousTier: currentTier,
          totalPoints: newTotalEarned,
        });

        await sendEmail({
          to: userDoc.email,
          subject: emailTemplate.subject,
          content: emailTemplate.content,
          type: emailTemplate.type,
        });

        console.log(
          `Tier upgrade email sent to ${userDoc.email}: ${currentTier} -> ${newTier}`
        );
      } catch (emailError) {
        console.error("Error sending tier upgrade email:", emailError);
      }
    }

    // Send points earned notification for positive points
    if (points > 0) {
      try {
        const userDoc = await admin.auth().getUser(userId);

        const {
          sendEmail,
          emailTemplates,
        } = require("./services/emailService");

        const emailTemplate = emailTemplates.pointsEarned({
          userEmail: userDoc.email,
          pointsEarned: points,
          totalPoints: newPoints,
          tier: newTier,
          reason: reason,
        });

        await sendEmail({
          to: userDoc.email,
          subject: emailTemplate.subject,
          content: emailTemplate.content,
          type: emailTemplate.type,
        });

        console.log(
          `Points earned email sent to ${userDoc.email}: +${points} points`
        );
      } catch (emailError) {
        console.error("Error sending points earned email:", emailError);
      }
    }

    return {
      success: true,
      points: newPoints,
      tier: newTier,
      tierUpgrade: newTier !== currentTier,
    };
  } catch (error) {
    console.error("Error updating loyalty points:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Award points when order is completed
exports.awardOrderPoints = functions.firestore
  .document("orders/{orderId}")
  .onCreate(async (snap, context) => {
    try {
      const order = snap.data();
      const orderId = context.params.orderId;

      // Only award points for completed orders with registered users
      if (
        order.userId &&
        order.userId !== "guest" &&
        order.status === "processing"
      ) {
        // Award 1 point per dollar spent
        const pointsToAward = Math.floor(order.total);

        const updateLoyaltyPoints = require("firebase-functions").httpsCallable(
          "updateLoyaltyPoints"
        );

        await updateLoyaltyPoints({
          userId: order.userId,
          points: pointsToAward,
          reason: `Order #${orderId}`,
          orderId: orderId,
        });

        console.log(
          `Awarded ${pointsToAward} points to user ${order.userId} for order ${orderId}`
        );
      }

      return null;
    } catch (error) {
      console.error("Error awarding order points:", error);
      return null;
    }
  });

// Send monthly loyalty program summary
// exports.sendMonthlyLoyaltySummary = functions.pubsub
  .schedule("0 9 1 * *")
  .onRun(async (context) => {
    try {
      console.log("Sending monthly loyalty program summaries...");

      // Get all loyalty program members
      const loyaltyRef = admin.firestore().collection("loyaltyProgram");
      const loyaltySnapshot = await loyaltyRef.get();

      const { sendEmail, emailTemplates } = require("./services/emailService");

      for (const loyaltyDoc of loyaltySnapshot.docs) {
        const loyaltyData = loyaltyDoc.data();

        try {
          // Get user email
          const userDoc = await admin.auth().getUser(loyaltyData.userId);

          // Get last month's transactions
          const lastMonth = new Date();
          lastMonth.setMonth(lastMonth.getMonth() - 1);
          lastMonth.setDate(1);
          lastMonth.setHours(0, 0, 0, 0);

          const thisMonth = new Date();
          thisMonth.setDate(1);
          thisMonth.setHours(0, 0, 0, 0);

          const transactionsRef = admin
            .firestore()
            .collection("loyaltyTransactions");
          const monthlyTransactions = await transactionsRef
            .where("userId", "==", loyaltyData.userId)
            .where(
              "createdAt",
              ">=",
              admin.firestore.Timestamp.fromDate(lastMonth)
            )
            .where(
              "createdAt",
              "<",
              admin.firestore.Timestamp.fromDate(thisMonth)
            )
            .get();

          const monthlyPointsEarned = monthlyTransactions.docs.reduce(
            (sum, doc) => sum + Math.max(0, doc.data().points),
            0
          );

          const emailTemplate = emailTemplates.monthlyLoyaltySummary({
            userEmail: userDoc.email,
            currentPoints: loyaltyData.points,
            tier: loyaltyData.tier,
            monthlyPointsEarned: monthlyPointsEarned,
            totalEarned: loyaltyData.totalEarned,
          });

          await sendEmail({
            to: userDoc.email,
            subject: emailTemplate.subject,
            content: emailTemplate.content,
            type: emailTemplate.type,
          });

          console.log(`Monthly loyalty summary sent to ${userDoc.email}`);
        } catch (emailError) {
          console.error(
            `Error sending monthly summary to user ${loyaltyData.userId}:`,
            emailError
          );
        }
      }

      return null;
    } catch (error) {
      console.error("Error sending monthly loyalty summaries:", error);
      return null;
    }
  });

// Import contact form function from separate file
const { sendContactFormReply } = require("./contactForm");
exports.sendContactFormReply = sendContactFormReply;

// Send welcome email (callable function)
exports.sendWelcomeEmail = functions.https.onCall(async (data, context) => {
  try {
    // Check if user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    const { email, name } = data;
    const userEmail = email || context.auth.token.email;
    const userName = name || context.auth.token.name || "Customer";

    if (!userEmail) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Email address is required"
      );
    }

    // Generate welcome email
    const emailTemplate = emailTemplates.welcomeEmail({
      name: userName,
      email: userEmail,
    });

    // Send welcome email
    const result = await sendEmail({
      to: userEmail,
      subject: emailTemplate.subject,
      content: emailTemplate.content,
      type: emailTemplate.type,
    });

    console.log("Welcome email sent to:", userEmail);

    return {
      success: true,
      message:
        result.status === "sent"
          ? "Welcome email sent successfully!"
          : "Welcome email logged",
      status: result.status,
    };
  } catch (error) {
    console.error("Error sending welcome email:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Get email logs (Admin function) - FIXED 1st Gen
exports.getEmailLogs = functions.https.onCall(async (data, context) => {
  try {
    console.log("🔍 getEmailLogs called with data:", data);
    console.log("🔍 Context:", context);
    console.log("🔍 Context.auth:", context.auth);

    // Check if user is authenticated and has admin privileges
    if (!context.auth) {
      console.log("❌ No authentication context found");
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    console.log("✅ User auth token:", context.auth.token);
    console.log("✅ User UID:", context.auth.uid);

    // TEMPORARILY REMOVE ADMIN CHECK TO TEST
    // if (!context.auth.token.admin && !context.auth.token.editor) {
    //   console.log("User is not admin or editor");
    //   throw new functions.https.HttpsError(
    //     "permission-denied",
    //     "Only admins or editors can view email logs"
    //   );
    // }

    const { limit = 50, startAfter, type } = data;

    // Build query
    let query = admin
      .firestore()
      .collection("email_logs")
      .orderBy("timestamp", "desc");

    // Apply filters
    if (type && type !== "all") {
      query = query.where("type", "==", type);
    }

    // Apply pagination
    if (startAfter) {
      const startAfterDoc = await admin
        .firestore()
        .collection("email_logs")
        .doc(startAfter)
        .get();
      if (startAfterDoc.exists) {
        query = query.startAfter(startAfterDoc);
      }
    }

    query = query.limit(limit);

    // Execute query
    const snapshot = await query.get();

    // Format results
    const logs = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate()?.toISOString() || null,
      createdAt: doc.data().createdAt?.toDate()?.toISOString() || null,
    }));

    return {
      logs,
      hasMore: snapshot.docs.length === limit,
      lastVisible:
        snapshot.docs.length > 0
          ? snapshot.docs[snapshot.docs.length - 1].id
          : null,
    };
  } catch (error) {
    console.error("Error getting email logs:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// SIMPLE EMAIL LOGS FUNCTION - NO AUTH FOR TESTING
exports.getEmailLogsSimple = functions.https.onCall(async (data, context) => {
  try {
    console.log("🔍 Simple email logs function called");

    // Just return emails from Firestore without auth checks
    const snapshot = await admin
      .firestore()
      .collection("email_logs")
      .orderBy("timestamp", "desc")
      .limit(10)
      .get();

    const logs = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate()?.toISOString() || null,
      createdAt: doc.data().createdAt?.toDate()?.toISOString() || null,
    }));

    console.log("✅ Found", logs.length, "email logs");
    return { logs, hasMore: false };
  } catch (error) {
    console.error("❌ Error in simple email logs:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});
