import { motion } from "framer-motion";
import styled from "styled-components";
import { colors, spacing, typography, breakpoints } from "../../styles";
import { FaGift, FaPercent, FaShippingFast, FaClock } from "react-icons/fa";

const OffersSection = styled.section`
  padding: ${spacing["3xl"]} ${spacing.lg};
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.lightest};
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: ${spacing.xl};
`;

const Title = styled(motion.h2)`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize["2xl"]};
  margin-bottom: ${spacing.md};

  @media (max-width: ${breakpoints.md}) {
    font-size: ${typography.fontSize.xl};
  }
`;

const OffersGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${spacing.lg};

  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const OfferCard = styled(motion.div)`
  background: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  border-radius: 16px;
  padding: ${spacing.xl};
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
`;

const OfferIcon = styled.div`
  width: 70px;
  height: 70px;
  margin: 0 auto ${spacing.md};
  border-radius: 50%;
  background: ${colors.primary.main};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
`;

const OfferTitle = styled.h3`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize.lg};
  margin-bottom: ${spacing.sm};
  font-weight: ${typography.fontWeight.bold};
`;

const OfferDescription = styled.p`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.gray};
  font-size: ${typography.fontSize.md};
  margin-bottom: ${spacing.md};
  line-height: 1.6;
`;

const OfferCode = styled.div`
  background: ${colors.secondary.main};
  color: white;
  padding: ${spacing.sm} ${spacing.md};
  border-radius: 15px;
  font-weight: ${typography.fontWeight.bold};
  font-size: ${typography.fontSize.sm};
  display: inline-block;
  margin-bottom: ${spacing.md};
`;

const OfferExpiry = styled.div`
  color: ${colors.neutral.gray};
  font-size: ${typography.fontSize.xs};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${spacing.xs};
`;

const SpecialOffers = ({ darkMode }) => {
  const offers = [
    {
      icon: FaPercent,
      title: "First Order Discount",
      description:
        "Get 20% off your first order when you sign up for our newsletter!",
      code: "WELCOME20",
      expiry: "Valid for new customers only",
    },
    {
      icon: FaShippingFast,
      title: "Free Shipping",
      description:
        "Enjoy free shipping on all orders over $50. No code needed!",
      code: "AUTO APPLIED",
      expiry: "On orders $50+",
    },
    {
      icon: FaGift,
      title: "Bundle & Save",
      description: "Buy any 3 products and get the 4th one absolutely free!",
      code: "BUY3GET1",
      expiry: "Limited time offer",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <OffersSection darkMode={darkMode}>
      <Container>
        <SectionHeader>
          <Title
            darkMode={darkMode}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Special Offers Just for You! 🎁
          </Title>
        </SectionHeader>

        <OffersGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {offers.map((offer, index) => (
            <OfferCard
              key={index}
              darkMode={darkMode}
              variants={cardVariants}
              whileHover={{
                y: -10,
                scale: 1.02,
                transition: { duration: 0.3 },
              }}
            >
              <OfferIcon>
                <offer.icon />
              </OfferIcon>

              <OfferTitle darkMode={darkMode}>{offer.title}</OfferTitle>

              <OfferDescription darkMode={darkMode}>
                {offer.description}
              </OfferDescription>

              <OfferCode>{offer.code}</OfferCode>

              <OfferExpiry>
                <FaClock />
                {offer.expiry}
              </OfferExpiry>
            </OfferCard>
          ))}
        </OffersGrid>
      </Container>
    </OffersSection>
  );
};

export default SpecialOffers;
