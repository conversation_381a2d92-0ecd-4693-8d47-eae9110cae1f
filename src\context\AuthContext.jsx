import { createContext, useContext, useState, useEffect } from "react";
import {
  auth,
  refreshTokenWithBackoff,
  googleProvider,
} from "../firebase/config";
import {
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  updateProfile,
  signOut,
  sendPasswordResetEmail,
} from "firebase/auth";

const AuthContext = createContext();

export const useAuth = () => {
  return useContext(AuthContext);
};

// Helper function to save the previous location
const savePreviousLocation = (path) => {
  if (path && !path.includes("/login") && !path.includes("/register")) {
    localStorage.setItem("authRedirectPath", path);
  }
};

// Helper function to get the saved location
const getSavedLocation = () => {
  return localStorage.getItem("authRedirectPath") || "/account";
};

// Helper function to clear the saved location
const clearSavedLocation = () => {
  localStorage.removeItem("authRedirectPath");
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userClaims, setUserClaims] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastRefreshTime, setLastRefreshTime] = useState(null);

  // Login function
  const login = async (email, password) => {
    setError("");
    try {
      await signInWithEmailAndPassword(auth, email, password);
      return true;
    } catch (error) {
      setError(error.message);
      return false;
    }
  };

  // Register function
  const register = async (email, password, name) => {
    setError("");
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      await updateProfile(userCredential.user, { displayName: name });
      return true;
    } catch (error) {
      setError(error.message);
      return false;
    }
  };

  // Logout function
  const logout = async () => {
    setError("");
    try {
      await signOut(auth);
      return true;
    } catch (error) {
      setError(error.message);
      return false;
    }
  };

  // Centralized function to refresh user claims
  const refreshUserClaims = async () => {
    if (!currentUser) return null;

    // Check if we've refreshed recently (add this to prevent excessive refreshes)
    const now = Date.now();
    if (lastRefreshTime && now - lastRefreshTime < 60000) {
      // 1 minute cooldown
      console.log("Token was refreshed recently, skipping refresh");
      return userClaims;
    }

    try {
      // Force token refresh
      await currentUser.getIdToken(true);

      // Get the refreshed token
      const idTokenResult = await currentUser.getIdTokenResult();

      // Update user claims state
      setUserClaims(idTokenResult.claims);

      // Update last refresh time
      setLastRefreshTime(now);

      return idTokenResult.claims;
    } catch (error) {
      console.error("Error refreshing token:", error);
      return null;
    }
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    return userClaims && userClaims[role] === true;
  };

  // Google sign-in function
  const signInWithGoogle = async () => {
    setError("");
    try {
      await signInWithPopup(auth, googleProvider);
      return true;
    } catch (error) {
      console.error("Google sign-in error:", error);

      // Provide user-friendly error messages
      switch (error.code) {
        case "auth/popup-closed-by-user":
          setError(
            "Sign-in was cancelled. Please try again when you're ready."
          );
          break;
        case "auth/popup-blocked":
          setError(
            "Sign-in popup was blocked by your browser. Please allow popups for this site and try again."
          );
          break;
        case "auth/cancelled-popup-request":
          setError("The sign-in process was interrupted. Please try again.");
          break;
        case "auth/account-exists-with-different-credential":
          setError(
            "An account already exists with the same email address but different sign-in credentials. Try signing in using a different method."
          );
          break;
        case "auth/network-request-failed":
          setError(
            "Network connection issue. Please check your internet connection and try again."
          );
          break;
        default:
          setError(
            "Something went wrong with Google sign-in. Please try again or use email sign-in instead."
          );
      }
      return false;
    }
  };

  // Password reset function
  const resetPassword = async (email) => {
    setError("");
    try {
      await sendPasswordResetEmail(auth, email);
      return true;
    } catch (error) {
      console.error("Password reset error:", error);

      switch (error.code) {
        case "auth/invalid-email":
          setError("Please enter a valid email address.");
          break;
        case "auth/user-not-found":
          setError("No account found with this email address.");
          break;
        case "auth/too-many-requests":
          setError("Too many attempts. Please try again later.");
          break;
        default:
          setError("Failed to send password reset email. Please try again.");
      }
      return false;
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);

      if (user) {
        try {
          const idTokenResult = await user.getIdTokenResult();
          setUserClaims(idTokenResult.claims);
        } catch (error) {
          console.error("Error getting token in auth state change:", error);
        }
      } else {
        setUserClaims(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    userClaims,
    isAuthenticated: !!currentUser,
    isAdmin: hasRole("admin"),
    isEditor: hasRole("editor"),
    isModerator: hasRole("moderator"),
    loading,
    error,
    login,
    register,
    logout,
    refreshUserClaims,
    hasRole,
    signInWithGoogle,
    resetPassword,
    savePreviousLocation,
    getSavedLocation,
    clearSavedLocation,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
