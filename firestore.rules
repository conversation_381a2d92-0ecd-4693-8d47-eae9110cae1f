rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check admin or editor role
    function isAdminOrEditor() {
      return request.auth != null &&
        (request.auth.token.admin == true || request.auth.token.editor == true);
    }

    // Helper function to check admin role
    function isAdmin() {
      return request.auth != null && request.auth.token.admin == true;
    }

    // Public product data can be read by anyone
    match /products/{productId} {
      allow read: if true;
      allow create, update: if isAdminOrEditor();
      allow delete: if isAdmin();
    }

    // Blog posts are public for reading
    match /blogPosts/{postId} {
      allow read: if true;
      allow create, update: if isAdminOrEditor();
      allow delete: if isAdmin();
    }

    // Contact messages can be created by anyone, but only read by admins
    match /messages/{messageId} {
      allow create: if true;
      allow read, update, delete: if isAdmin();
    }

    // User profiles - allow users to manage their own profiles
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // Only admins can read other users' profiles
      allow read: if isAdmin();

      // Add this new rule for addresses subcollection
      match /addresses/{addressId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    // Product reviews - anyone can read, authenticated users can create
    match /productReviews/{reviewId} {
      allow read: if true;
      allow create: if request.auth != null;
      // Users can only update or delete their own reviews
      allow update, delete: if request.auth != null && request.auth.uid == resource.data.userId;
      // Admins can update or delete any review
      allow update, delete: if isAdmin();
    }

    // Orders - users can create and read their own orders, admins can manage all
    match /orders/{orderId} {
      // Allow authenticated users to create orders
      allow create: if request.auth != null;
      // Allow guest users to create orders (for guest checkout)
      allow create: if request.auth == null &&
        request.resource.data.userId == "guest" &&
        request.resource.data.userEmail is string &&
        request.resource.data.userEmail.size() > 0;
      // Allow users to read their own orders (both authenticated users and guests by email)
      allow read: if request.auth != null &&
        (request.auth.uid == resource.data.userId ||
         request.auth.token.email == resource.data.userEmail);
      // Allow guests to read orders by matching email (when authenticated)
      allow read: if resource.data.userId == "guest" &&
        request.auth != null &&
        request.auth.token.email == resource.data.userEmail;
      // Allow unauthenticated users to read guest orders (for immediate verification after creation)
      allow read: if request.auth == null && resource.data.userId == "guest";
      // Admins can read and update all orders
      allow read, update: if isAdmin();
      // Only admins can delete orders
      allow delete: if isAdmin();
    }

    // Email logs - only admins and editors can access
    match /email_logs/{emailId} {
      allow read, write: if isAdminOrEditor();
    }
  }
}






