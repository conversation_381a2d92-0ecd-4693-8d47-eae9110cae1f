const admin = require("firebase-admin");
const functions = require("firebase-functions");
// Temporarily avoid importing emailService to prevent deployment timeout
// const { sendEmail, emailTemplates } = require("./services/emailService");

// Initialize admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Simple contact form auto-reply function
exports.sendContactFormReply = functions.https.onCall(async (data, context) => {
  try {
    console.log("=== CONTACT FORM REPLY DEBUG ===");
    console.log("Raw data received:", data);
    console.log("Data type:", typeof data);
    console.log("Data keys:", Object.keys(data || {}));

    // Handle different data structures
    let formData = data;

    // Check if data is nested (common with Firebase callable functions)
    if (data && typeof data === "object" && !data.name && !data.email) {
      // Look for nested data
      if (data.data) {
        formData = data.data;
        console.log("Using nested data.data:", formData);
      } else if (data.formData) {
        formData = data.formData;
        console.log("Using nested data.formData:", formData);
      }
    }

    const { name, email, subject, message } = formData;
    console.log("Final extracted fields:", { name, email, subject, message });

    // More robust email validation
    if (!email || typeof email !== "string" || email.trim() === "") {
      console.error("Email validation failed:");
      console.error("- email value:", email);
      console.error("- email type:", typeof email);
      console.error("- email length:", email?.length);
      console.error("- formData:", formData);
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Valid email address is required"
      );
    }

    console.log("Contact form auto-reply request:", { name, email, subject });

    // Temporarily log only to avoid deployment timeout
    // TODO: Re-enable email sending after main functions are deployed
    console.log("Email would be sent to:", email);
    console.log("Subject:", `Thank you for contacting us - ${subject}`);

    // Log to Firestore directly
    try {
      await admin
        .firestore()
        .collection("email_logs")
        .add({
          to: email,
          subject: `Thank you for contacting us - ${subject}`,
          content: `Hi ${name}, we received your message and will get back to you soon.`,
          type: "support",
          status: "logged_only",
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          note: "Contact form auto-reply logged (SendGrid temporarily disabled)",
        });
      console.log("Email logged to Firestore successfully");
    } catch (logError) {
      console.error("Error logging email:", logError);
    }

    return {
      success: true,
      message: "Contact form received and auto-reply logged",
      status: "logged_only",
    };
  } catch (error) {
    console.error("Error processing contact form:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});
