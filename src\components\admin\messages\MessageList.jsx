import React from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaEnvelope,
  FaEnvelopeOpen,
  FaStar,
  FaArchive,
  FaTrash,
  FaCheck,
  FaSquare,
} from "react-icons/fa";
import { colors, spacing, breakpoints } from "../../../styles";
import {
  formatReadTimestamp,
  formatMessageContent,
  highlightSearchTerms,
} from "./MessageUtils";
import MessageDetail from "./MessageDetail";
import DeleteConfirmationModal from "../../ui/DeleteConfirmationModal";

// Add checkbox styling
const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  margin-right: ${spacing.sm};
  cursor: pointer;
  color: ${(props) =>
    props.$isSelected
      ? colors.primary?.main || "#0088ff"
      : props.darkMode
      ? colors.neutral.lighter
      : colors.neutral.darker};
`;

// Replace the gray archive color with the purple one used for the archive icon
const ARCHIVE_COLOR = "#9c27b0"; // Purple for archived items (matching the icon)

const ListContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.md};
  width: 100%;
`;

const MessageCard = styled.div`
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-left: 3px solid
    ${(props) => {
      if (props.isStarred) return colors.warning?.main || "#ffbb00";
      if (!props.isRead) return colors.info?.main || "#0088ff";
      if (props.isArchived) return ARCHIVE_COLOR;
      if (props.isRead) return colors.success?.main || "#4CAF50";
      return "transparent";
    }};

  @media (max-width: ${breakpoints.sm}) {
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
`;

const MessageHeader = styled.div`
  padding: ${spacing.md};
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.lightest};

  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.sm};
  }
`;

const MessageInfo = styled.div`
  flex: 1;
`;

const MessageSubject = styled.h3`
  margin: 0 0 ${spacing.xs} 0;
  font-size: 16px;
  font-weight: 600;
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};

  @media (max-width: ${breakpoints.sm}) {
    font-size: 14px;
  }
`;

const MessagePreview = styled.p`
  margin: 0;
  font-size: 14px;
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;

  @media (max-width: ${breakpoints.sm}) {
    font-size: 12px;
  }
`;

const MessageMeta = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: ${spacing.xs};
`;

const MessageDate = styled.span`
  font-size: 14px;
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const MessageReadInfo = styled.span`
  font-size: 12px;
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.medium};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${spacing.sm};
  margin-top: ${spacing.md};
  flex-wrap: wrap;

  @media (max-width: ${breakpoints.sm}) {
    gap: ${spacing.xs};
  }
`;

const ActionButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  padding: 0;
  border: none;
  border-radius: 4px;
  background: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.light};
  color: ${(props) => {
    if (props.className === "read") return colors.success?.main || "#4CAF50";
    if (props.className === "unread") return colors.info?.main || "#0088ff";
    if (props.className === "star") return colors.warning?.main || "#ffbb00";
    if (props.className === "archive") return "#9c27b0"; // Purple for archive
    if (props.className === "delete") return "#8B0000"; // Deep blood red
    return props.darkMode ? colors.neutral.lighter : colors.neutral.darker;
  }};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${(props) => {
      if (props.className === "read")
        return `${colors.success?.main || "#4CAF50"}20`;
      if (props.className === "unread")
        return `${colors.info?.main || "#0088ff"}20`;
      if (props.className === "star")
        return `${colors.warning?.main || "#ffbb00"}20`;
      if (props.className === "archive") return "#9c27b020";
      if (props.className === "delete") return "#8B000020";
      return props.darkMode ? colors.neutral.medium : colors.neutral.medium;
    }};
  }
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: ${spacing.sm};
  margin-top: ${spacing.lg};
`;

const PageButton = styled(motion.button)`
  padding: ${spacing.sm} ${spacing.md};
  background: ${(props) =>
    props.active
      ? props.darkMode
        ? colors.primary.dark
        : colors.primary.light
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.light};
  color: ${(props) =>
    props.active
      ? props.darkMode
        ? colors.neutral.white
        : colors.primary.dark
      : props.darkMode
      ? colors.neutral.light
      : colors.neutral.dark};
  border: none;
  border-radius: 4px;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};
`;

const MessageContentWrapper = styled(motion.div)`
  padding: ${spacing.md};
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};

  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.sm};
  }
`;

const MessageSender = styled.div`
  font-size: 14px;
  margin-bottom: ${spacing.sm};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const MessageBody = styled.div`
  font-size: 14px;
  line-height: 1.6;
  color: ${(props) =>
    props.darkMode ? colors.neutral.lighter : colors.neutral.darker};
  white-space: pre-wrap;
  margin-bottom: ${spacing.md};
`;

// Add this styled component for the highlighted text
const HighlightStyle = styled.div`
  mark {
    background-color: ${(props) => (props.darkMode ? "#ffcc00" : "#ffff00")};
    color: ${(props) => (props.darkMode ? "#000000" : "#000000")};
    padding: 0 2px;
    border-radius: 2px;
  }
`;

const MessageList = ({
  darkMode,
  messages,
  expandedMessages,
  toggleMessageExpansion,
  handleToggleReadStatus,
  handleToggleStar,
  handleToggleArchiveStatus,
  handleDeleteMessage,
  currentPage,
  totalPages,
  handlePageChange,
  selectedMessages = [],
  toggleMessageSelection,
  isSelectionMode = false,
  searchQuery = "", // Add this prop
}) => {
  const [deleteModalOpen, setDeleteModalOpen] = React.useState(false);
  const [selectedMessageId, setSelectedMessageId] = React.useState(null);

  return (
    <ListContainer>
      <HighlightStyle darkMode={darkMode} />
      {messages.length === 0 ? (
        <div
          style={{
            textAlign: "center",
            padding: spacing.lg,
            color: darkMode ? colors.neutral.light : colors.neutral.dark,
          }}
        >
          No messages found matching your filters.
        </div>
      ) : (
        <>
          {messages.map((message) => (
            <MessageCard
              key={message.id}
              darkMode={darkMode}
              isRead={message.isRead}
              isStarred={message.isStarred}
              isArchived={message.isArchived}
              isSelected={selectedMessages.includes(message.id)}
            >
              <MessageHeader darkMode={darkMode}>
                {isSelectionMode && (
                  <CheckboxContainer
                    darkMode={darkMode}
                    $isSelected={selectedMessages.includes(message.id)}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleMessageSelection(message.id);
                    }}
                  >
                    {selectedMessages.includes(message.id) ? (
                      <FaCheck />
                    ) : (
                      <FaSquare />
                    )}
                  </CheckboxContainer>
                )}
                <MessageInfo onClick={() => toggleMessageExpansion(message.id)}>
                  <MessageSubject
                    darkMode={darkMode}
                    dangerouslySetInnerHTML={{
                      __html: searchQuery
                        ? highlightSearchTerms(
                            message.subject || "No Subject",
                            searchQuery
                          )
                        : message.subject || "No Subject",
                    }}
                  />
                </MessageInfo>
                <MessageMeta>
                  <MessageDate darkMode={darkMode}>
                    {message.date
                      ? new Date(message.date).toLocaleDateString()
                      : "No date"}
                  </MessageDate>
                  {message.isRead && message.readAt && (
                    <MessageReadInfo darkMode={darkMode}>
                      Read: {formatReadTimestamp(message.readAt)}
                    </MessageReadInfo>
                  )}
                </MessageMeta>
              </MessageHeader>

              <AnimatePresence>
                {expandedMessages.includes(message.id) && (
                  <MessageContentWrapper
                    darkMode={darkMode}
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <MessageSender
                      darkMode={darkMode}
                      dangerouslySetInnerHTML={{
                        __html: searchQuery
                          ? `From: ${highlightSearchTerms(
                              message.name || "Unknown",
                              searchQuery
                            )} (${highlightSearchTerms(
                              message.email || "No email",
                              searchQuery
                            )})`
                          : `From: ${message.name || "Unknown"} (${
                              message.email || "No email"
                            })`,
                      }}
                    />

                    <MessageBody
                      darkMode={darkMode}
                      expanded={true}
                      dangerouslySetInnerHTML={{
                        __html: formatMessageContent(
                          message.message,
                          searchQuery
                        ),
                      }}
                    />

                    <ActionButtons>
                      <ActionButton
                        darkMode={darkMode}
                        className={message.isRead ? "read" : "unread"}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleReadStatus(message.id);
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        title={
                          message.isRead ? "Mark as unread" : "Mark as read"
                        }
                      >
                        {message.isRead ? <FaEnvelopeOpen /> : <FaEnvelope />}
                      </ActionButton>

                      <ActionButton
                        darkMode={darkMode}
                        className="star"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStar(message.id);
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        title={message.isStarred ? "Unstar" : "Star"}
                      >
                        <FaStar />
                      </ActionButton>

                      <ActionButton
                        darkMode={darkMode}
                        className="archive"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleArchiveStatus(message.id);
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        title={message.isArchived ? "Unarchive" : "Archive"}
                      >
                        <FaArchive />
                      </ActionButton>

                      <ActionButton
                        darkMode={darkMode}
                        className="delete"
                        onClick={(e) => {
                          e.stopPropagation();
                          setDeleteModalOpen(true);
                          setSelectedMessageId(message.id);
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        title="Delete"
                      >
                        <FaTrash />
                      </ActionButton>
                    </ActionButtons>
                  </MessageContentWrapper>
                )}
              </AnimatePresence>
            </MessageCard>
          ))}

          {totalPages > 1 && (
            <PaginationContainer>
              <PageButton
                darkMode={darkMode}
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                whileHover={{ scale: currentPage === 1 ? 1 : 1.05 }}
                whileTap={{ scale: currentPage === 1 ? 1 : 0.95 }}
              >
                Previous
              </PageButton>

              {[...Array(totalPages).keys()].map((page) => (
                <PageButton
                  key={page + 1}
                  active={currentPage === page + 1}
                  darkMode={darkMode}
                  onClick={() => handlePageChange(page + 1)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {page + 1}
                </PageButton>
              ))}

              <PageButton
                darkMode={darkMode}
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                whileHover={{ scale: currentPage === totalPages ? 1 : 1.05 }}
                whileTap={{ scale: currentPage === totalPages ? 1 : 0.95 }}
              >
                Next
              </PageButton>
            </PaginationContainer>
          )}
        </>
      )}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen && selectedMessageId !== null}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={() => {
          handleDeleteMessage(selectedMessageId);
          setDeleteModalOpen(false);
        }}
        itemName={
          messages.find((m) => m.id === selectedMessageId)?.subject ||
          "this message"
        }
        darkMode={darkMode}
      />
    </ListContainer>
  );
};

export default MessageList;
